"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Skeleton } from "@/components/ui/skeleton"
import { useUser } from "@/lib/user-store/provider"
import { User, X, FloppyDisk } from "@phosphor-icons/react"
import { useEffect, useState } from "react"

interface EditProfileProps {
  onSave?: () => void
  onCancel?: () => void
}

interface ProfileFormData {
  displayName: string
}

export function EditProfile({ onSave, onCancel }: EditProfileProps) {
  const { user, updateUser } = useUser()
  const [formData, setFormData] = useState<ProfileFormData>({
    displayName: ""
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Initialize form data when user loads
  useEffect(() => {
    if (user) {
      setFormData({
        displayName: user.display_name || ""
      })
    }
  }, [user])

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.displayName.trim()) {
      newErrors.displayName = "Display name is required"
    } else if (formData.displayName.trim().length < 2) {
      newErrors.displayName = "Display name must be at least 2 characters"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = async () => {
    if (!validateForm() || !user?.id) return

    setLoading(true)
    try {
      const response = await fetch(`/api/users/${user.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          display_name: formData.displayName.trim()
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to update profile')
      }

      const updatedUser = await response.json()
      updateUser(updatedUser)
      onSave?.()
    } catch (error) {
      console.error('Failed to update profile:', error)
      setErrors({ general: error instanceof Error ? error.message : 'Failed to update profile' })
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    if (user) {
      setFormData({
        displayName: user.display_name || ""
      })
    }
    setErrors({})
    onCancel?.()
  }

  // Show skeleton loader when no user
  if (!user) {
    return (
      <div className="space-y-6">
        <div>
          <Skeleton className="h-5 w-24 mb-4" />
          <div className="space-y-4">
            <div className="flex flex-col items-center space-y-4">
              <Skeleton className="size-20 rounded-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="mb-4 text-lg font-medium">Edit Profile</h3>
        
        {/* General Error */}
        {errors.general && (
          <div className="mb-4 text-sm text-red-500 bg-red-50 p-3 rounded-md">
            {errors.general}
          </div>
        )}

        <div className="space-y-6">
          {/* Current Profile Image Display (Read-only) */}
          <div className="flex flex-col items-center space-y-2">
            <div className="bg-muted flex items-center justify-center overflow-hidden rounded-full">
              {user?.profile_image ? (
                <Avatar className="size-20">
                  <AvatarImage src={user.profile_image} className="object-cover" />
                  <AvatarFallback>{user?.display_name?.charAt(0)}</AvatarFallback>
                </Avatar>
              ) : (
                <User className="text-muted-foreground size-20" />
              )}
            </div>
          </div>

          {/* Display Name Field */}
          <div className="space-y-2">
            <Label htmlFor="displayName" className="text-sm font-medium">
              Display Name
            </Label>
            <Input
              id="displayName"
              type="text"
              value={formData.displayName}
              onChange={(e) => {
                setFormData(prev => ({ ...prev, displayName: e.target.value }))
                if (errors.displayName) {
                  setErrors(prev => ({ ...prev, displayName: "" }))
                }
              }}
              placeholder="Enter your display name"
              className={errors.displayName ? "border-red-500" : ""}
            />
            {errors.displayName && (
              <p className="text-xs text-red-500">{errors.displayName}</p>
            )}
          </div>

          {/* Current Email Display (Read-only) */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-muted-foreground">
              Email
            </Label>
            <div className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
              {user?.email}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              onClick={handleSave}
              disabled={loading}
              className="flex-1"
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <Skeleton className="w-4 h-4" />
                  <span>Saving...</span>
                </div>
              ) : (
                <>
                  <FloppyDisk className="w-4 h-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
            
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
              className="flex-1"
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
