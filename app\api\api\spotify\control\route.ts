// /app/api/spotify/control/route.ts
import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { createClient } from '@supabase/supabase-js';

// Direct Supabase client initialization
const supabase = createClient(
  'https://uvqumfsecdnmzueqdocd.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.bw8jKjf9-tFU61t1bNEB2Xeu3N7aLXFcvYHlIzZvna4'
);

export async function POST(request: NextRequest) {
  try {
    const { userId, action } = await request.json();

    if (!userId || !action) {
      return NextResponse.json({ error: 'Missing userId or action' }, { status: 400 });
    }

    // Get user's Spotify account
    const { data: spotifyAccount, error } = await supabase
      .from('spotify_accounts')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error || !spotifyAccount) {
      return NextResponse.json({ error: 'Spotify account not linked' }, { status: 404 });
    }

    // Check if token needs refresh
    const now = new Date();
    let accessToken = spotifyAccount.access_token;

    if (new Date(spotifyAccount.expires_at) <= now) {
      try {
        const tokenResponse = await axios.post(
          'https://accounts.spotify.com/api/token',
          new URLSearchParams({
            grant_type: 'refresh_token',
            refresh_token: spotifyAccount.refresh_token,
            client_id: process.env.SPOTIFY_CLIENT_ID!,
            client_secret: process.env.SPOTIFY_CLIENT_SECRET!,
          }),
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          }
        );

        accessToken = tokenResponse.data.access_token;
        const expiresIn = tokenResponse.data.expires_in;
        const newExpiresAt = new Date();
        newExpiresAt.setSeconds(newExpiresAt.getSeconds() + expiresIn);

        await supabase
          .from('spotify_accounts')
          .update({
            access_token: accessToken,
            expires_at: newExpiresAt.toISOString(),
          })
          .eq('user_id', userId);
      } catch (refreshError) {
        return NextResponse.json({ error: 'Token refresh failed' }, { status: 401 });
      }
    }

    // Control Spotify player
    let endpoint = '';
    let method = 'POST';

    switch (action) {
      case 'play':
        endpoint = 'https://api.spotify.com/v1/me/player/play';
        method = 'PUT';
        break;
      case 'pause':
        endpoint = 'https://api.spotify.com/v1/me/player/pause';
        method = 'PUT';
        break;
      case 'next':
        endpoint = 'https://api.spotify.com/v1/me/player/next';
        method = 'POST';
        break;
      case 'previous':
        endpoint = 'https://api.spotify.com/v1/me/player/previous';
        method = 'POST';
        break;
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    await axios({
      method,
      url: endpoint,
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    return NextResponse.json({ success: true, action });
  } catch (error: any) {
    console.error('Spotify control error:', error);
    return NextResponse.json({ 
      error: error?.response?.data?.error?.message || 'Failed to control Spotify' 
    }, { status: 500 });
  }
}
