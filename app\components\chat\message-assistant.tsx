import {
  Message,
  MessageAction,
  MessageActions,
  MessageContent,
} from "@/components/prompt-kit/message";
import { useUserPreferences } from "@/lib/user-preference-store/provider";
import { cn } from "@/lib/utils";
import type { Message as MessageAISDK, ToolInvocationUIPart } from "@ai-sdk/react";
import { ArrowClockwise, Check, Copy, MusicNote, Wifi, WifiSlash, WifiX } from "@phosphor-icons/react";
import { useCallback, useRef, useState } from "react";
import { getSources } from "./get-sources";
import { ProductSuggestions } from "./product-suggestions";
import { QuoteButton } from "./quote-button";
import { Reasoning } from "./reasoning";
import { SearchImages } from "./search-images";
import { SourcesList } from "./sources-list";
import { ToolInvocation } from "./tool-invocation";
import { useAssistantMessageSelection } from "./useAssistantMessageSelection";
import { useSpotifyRealTime } from "@/app/hooks/useSpotifyRealTime";
import { ChartDisplay } from '@/components/ui/chart-display';
import MapWrapper from '@/components/map/MapWrapper';
import { JobSuggestions, type JobResult } from './job-suggestions';
import { CardDisplay } from './CardDisplay';

type SpotifyStatus = {
  linked: boolean;
  currentlyPlaying: string | null;
  loginUrl?: string;
  isPlaying?: boolean;
  track?: {
    name: string;
    artist: string;
    album?: string;
    duration?: number;
    position?: number;
  };
};

type MessageAssistantProps = {
  children: string;
  isLast?: boolean;
  hasScrollAnchor?: boolean;
  copied?: boolean;
  copyToClipboard?: () => void;
  onReload?: () => void;
  parts?: MessageAISDK["parts"];
  status?: "streaming" | "ready" | "submitted" | "error";
  className?: string;
  messageId: string;
  onQuote?: (text: string, messageId: string) => void;
  userId?: string;
  onKnowMore?: (productName: string) => void;
  onApplyJob?: (jobUrl: string, jobTitle: string) => void; // Add this line
};

// Spotify Status Card Component
const SpotifyStatusCard = ({
  statusData,
  realTimeStatus,
  userId,
  onSpotifyControl,
  loading,
  error,
  connected,
  onRefresh
}: {
  statusData?: any;
  realTimeStatus?: any;
  userId?: string;
  onSpotifyControl: (action: 'play' | 'pause' | 'next' | 'previous') => void;
  loading?: boolean;
  error?: string | null;
  connected?: boolean;
  onRefresh?: () => void;
}) => {
  const currentStatus = realTimeStatus || statusData;

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const progressPercentage = currentStatus?.track?.progress_ms && currentStatus?.track?.duration_ms
    ? (currentStatus.track.progress_ms / currentStatus.track.duration_ms) * 100
    : 0;

  if (loading && !currentStatus?.linked) {
    return (
      <div className="flex items-center gap-3 rounded-lg border bg-card p-4">
        <div className="animate-spin">
          <MusicNote className="size-6 text-muted-foreground" />
        </div>
        <div>
          <p className="font-medium">Loading Spotify Status...</p>
          <p className="text-sm text-muted-foreground">Fetching current playback information</p>
        </div>
      </div>
    );
  }

  if (!currentStatus?.linked) {
    return (
      <div className="flex items-center gap-3 rounded-lg border bg-card p-4">
        <MusicNote className="size-6 text-muted-foreground" />
        <div className="flex-1">
          <p className="font-medium">Spotify Not Connected</p>
          <p className="text-sm text-muted-foreground">Link your Spotify account to see what's playing</p>
          {currentStatus?.loginUrl && (
            <a
              href={currentStatus.loginUrl}
              className="text-sm text-primary hover:underline mt-1 inline-block"
            >
              Connect Spotify Account →
            </a>
          )}
        </div>
      </div>
    );
  }

  if (!currentStatus.currentlyPlaying) {
    return (
      <div className="flex items-center gap-3 rounded-lg border bg-card p-4">
        <MusicNote className="size-6 text-muted-foreground" />
        <div className="flex-1">
          <p className="font-medium">Nothing Playing</p>
          <p className="text-sm text-muted-foreground">Start playing music on Spotify</p>
        </div>
        {onRefresh && (
          <button
            onClick={onRefresh}
            className="text-muted-foreground hover:text-foreground transition-colors"
            title="Refresh status"
          >
            <ArrowClockwise className="size-4" />
          </button>
        )}
      </div>
    );
  }

  return (
    <div className="rounded-lg border bg-card p-4">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            {connected ? (
              <WifiX className="size-3 text-green-500" />
            ) : (
              <WifiSlash className="size-3 text-red-500" />
            )}
            <span className="text-xs text-muted-foreground">
              {connected ? 'Live' : 'Disconnected'}
            </span>
          </div>
          {connected && (
            <div className="size-2 bg-green-500 rounded-full animate-pulse" />
          )}
        </div>
        {onRefresh && (
          <button
            onClick={onRefresh}
            className="text-muted-foreground hover:text-foreground transition-colors"
            title="Refresh status"
          >
            <ArrowClockwise className={cn("size-4", loading && "animate-spin")} />
          </button>
        )}
      </div>

      {error && (
        <div className="mb-3 p-2 rounded bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}

      <div className="flex items-start gap-4">
        {currentStatus.track?.albumImage && (
          <div className="relative">
            <img
              src={currentStatus.track.albumImage}
              alt={`${currentStatus.track.album} album cover`}
              className="size-16 rounded-md object-cover"
            />
            {currentStatus.isPlaying && (
              <div className="absolute inset-0 rounded-md bg-black/20 flex items-center justify-center">
                <div className="flex space-x-0.5">
                  <div className="w-1 h-3 bg-white rounded-full animate-pulse" style={{ animationDelay: '0ms' }} />
                  <div className="w-1 h-3 bg-white rounded-full animate-pulse" style={{ animationDelay: '200ms' }} />
                  <div className="w-1 h-3 bg-white rounded-full animate-pulse" style={{ animationDelay: '400ms' }} />
                </div>
              </div>
            )}
          </div>
        )}

        <div className="flex-1 min-w-0">
          <h3 className="font-medium truncate">{currentStatus.track?.name}</h3>
          <p className="text-sm text-muted-foreground truncate">{currentStatus.track?.artist}</p>
          {currentStatus.track?.album && (
            <p className="text-xs text-muted-foreground truncate">{currentStatus.track.album}</p>
          )}

          {currentStatus.track?.duration_ms && currentStatus.track?.progress_ms !== undefined && (
            <div className="mt-3">
              <div className="flex justify-between text-xs text-muted-foreground mb-1">
                <span>{formatTime(currentStatus.track.progress_ms)}</span>
                <span>{formatTime(currentStatus.track.duration_ms)}</span>
              </div>
              <div className="w-full bg-muted rounded-full h-1">
                <div
                  className="bg-primary h-1 rounded-full transition-all duration-300"
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export function MessageAssistant({
  children,
  isLast,
  hasScrollAnchor,
  copied,
  copyToClipboard,
  onReload,
  parts,
  status,
  className,
  messageId,
  onQuote,
  userId,
  onKnowMore,
  onApplyJob,
}: MessageAssistantProps) {
  const { preferences } = useUserPreferences();
  const sources = getSources(parts);

  const [spotifyStatus, setSpotifyStatus] = useState<SpotifyStatus>({
    linked: false,
    currentlyPlaying: null,
  });
  const [spotifyLoading, setSpotifyLoading] = useState(false);

  const handleSpotifyControl = async (action: 'play' | 'pause' | 'next' | 'previous') => {
    if (!userId) return;

    setSpotifyLoading(true);
    try {
      const response = await fetch('/api/spotify/control', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, action }),
      });

      if (response.ok) {
        setTimeout(() => {
          realTimeRefetch();
        }, 1000);
      }
    } catch (error) {
      console.error('Spotify control error:', error);
    } finally {
      setSpotifyLoading(false);
    }
  };

  const toolInvocationParts: ToolInvocationUIPart[] =
    parts?.filter(
      (part): part is ToolInvocationUIPart =>
        part.type === "tool-invocation" && !!part.toolInvocation
    ) ?? [];

  const reasoningParts = parts?.find((part) => part.type === "reasoning");

  const spotifyStatusResults = toolInvocationParts
    .filter(
      (part) =>
        part.toolInvocation.state === "result" &&
        part.toolInvocation.toolName === "spotify_status"
    )
    .map((part) => part.toolInvocation.result);

  const {
    status: realTimeStatus,
    loading: realTimeLoading,
    error: realTimeError,
    connected,
    refetch: realTimeRefetch
  } = useSpotifyRealTime(
    userId,
    spotifyStatusResults.length > 0,
    false
  );

  const searchImageResults = toolInvocationParts
    .filter(
      (part) =>
        part.toolInvocation.state === "result" &&
        part.toolInvocation.toolName === "imageSearch"
    )
    .flatMap((part) => {
      const result = part.toolInvocation.result;
      if (result?.content && Array.isArray(result.content) && result.content[0]?.type === "images") {
        return result.content[0].results ?? [];
      }
      if (result?.results && Array.isArray(result.results)) {
        return result.results;
      }
      if (result?.content && Array.isArray(result.content)) {
        return result.content;
      }
      return [];
    });

  const productSuggestionResults = toolInvocationParts
    .filter(
      (part) =>
        part.toolInvocation.state === "result" &&
        part.toolInvocation.toolName === "suggest_products" &&
        part.toolInvocation.result?.results
    )
    .flatMap((part) => part.toolInvocation.result.results ?? []);

  const chartResults = toolInvocationParts
    .filter(
      (part) =>
        part.toolInvocation.state === "result" &&
        part.toolInvocation.toolName === "create_chart" &&
        part.toolInvocation.result?.chart
    )
    .map((part) => part.toolInvocation.result.chart);

  // Map search results filter
  const mapSearchResults = toolInvocationParts
    .filter(
      (part) =>
        part.toolInvocation.state === "result" &&
        (part.toolInvocation.toolName === "search_hotels_with_map" ||
          part.toolInvocation.toolName === "search_coaching_with_map") &&
        part.toolInvocation.result?.results
    )
    .flatMap((part) => part.toolInvocation.result.results ?? []);

  const jobSuggestionResults = toolInvocationParts
    .filter(
      (part) =>
        part.toolInvocation.state === "result" &&
        part.toolInvocation.toolName === "suggest_jobs" &&
        part.toolInvocation.result?.jobs
    )
    .flatMap((part) => part.toolInvocation.result.jobs ?? []);

  const jobSearchMetadata = toolInvocationParts
    .filter(
      (part) =>
        part.toolInvocation.state === "result" &&
        part.toolInvocation.toolName === "suggest_jobs"
    )
    .map((part) => part.toolInvocation.result.searchMetadata)?.[0];

  const cardResults = toolInvocationParts
    .filter(
      (part) =>
        part.toolInvocation.state === "result" &&
        part.toolInvocation.toolName === "show_cards" &&
        part.toolInvocation.result?.cards
    )
    .map((part) => ({
      cards: part.toolInvocation.result.cards,
      config: part.toolInvocation.result.config
    }));

  const handleApplyJob = useCallback((jobUrl: string, jobTitle: string) => {
    // If parent provided onApplyJob callback, use it
    if (onApplyJob) {
      onApplyJob(jobUrl, jobTitle);
    } else {
      // Default behavior: open job URL in new tab
      if (jobUrl) {
        window.open(jobUrl, '_blank');

        // Optional: You can also show a toast notification
        console.log(`Opening job application for: ${jobTitle}`);
      }
    }
  }, [onApplyJob]);

  const contentNullOrEmpty = children === null || children === "";
  const isQuoteEnabled = !preferences.multiModelEnabled;
  const messageRef = useRef<HTMLDivElement>(null);
  const { selectionInfo, clearSelection } = useAssistantMessageSelection(
    messageRef,
    isQuoteEnabled
  );

  const handleQuoteBtnClick = useCallback(() => {
    if (selectionInfo && onQuote) {
      onQuote(selectionInfo.text, selectionInfo.messageId);
      clearSelection();
    }
  }, [selectionInfo, onQuote, clearSelection]);

  const isLastStreaming = status === "streaming" && isLast;

  return (
    <Message
      className={cn(
        "group flex w-full max-w-3xl flex-1 items-start gap-4 px-6 pb-2",
        hasScrollAnchor && "min-h-scroll-anchor",
        className
      )}
    >
      <div
        ref={messageRef}
        className={cn("relative flex min-w-full flex-col gap-2", isLast && "pb-8")}
        {...(isQuoteEnabled && { "data-message-id": messageId })}
      >
        {reasoningParts?.reasoning && (
          <Reasoning
            reasoning={reasoningParts.reasoning}
            isStreaming={status === "streaming"}
          />
        )}

        {toolInvocationParts.length > 0 && preferences.showToolInvocations && (
          <ToolInvocation toolInvocations={toolInvocationParts} />
        )}

        {spotifyStatusResults.length > 0 && (
          <div className="space-y-3">
            <div className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <MusicNote className="size-4" />
              Spotify Status
              {connected && (
                <div className="flex items-center gap-1">
                  <div className="size-2 bg-green-500 rounded-full animate-pulse" />
                  <span className="text-xs">Live</span>
                </div>
              )}
            </div>
            {spotifyStatusResults.map((result, index) => (
              <SpotifyStatusCard
                key={index}
                statusData={result}
                realTimeStatus={realTimeStatus}
                userId={userId}
                onSpotifyControl={handleSpotifyControl}
                loading={realTimeLoading || spotifyLoading}
                error={realTimeError}
                connected={connected}
                onRefresh={realTimeRefetch}
              />
            ))}
          </div>
        )}

        {searchImageResults.length > 0 && (
          <>
            <div className="text-sm text-muted-foreground">
              Found {searchImageResults.length} images
            </div>
            <SearchImages results={searchImageResults} />
          </>
        )}

        {/* Map Display - यह सबसे important part है */}
        {mapSearchResults.length > 0 && (
          <div className="space-y-4">
            <MapWrapper
              results={mapSearchResults}
              mapCenter={toolInvocationParts.find(p =>
                p.toolInvocation.result?.mapCenter
              )?.toolInvocation.result.mapCenter}
              height="400px"
              zoom={13}
            />
          </div>
        )}

        {productSuggestionResults.length > 0 && (
          <ProductSuggestions
            results={productSuggestionResults}
            onKnowMore={onKnowMore}
          />
        )}

        {cardResults.length > 0 && (
          <div className="space-y-4"> 
            {cardResults.map((result, index) => (
              <div key={index}>
                <div className="text-sm font-medium text-muted-foreground mb-2">
                  {result.config.cardType.charAt(0).toUpperCase() + result.config.cardType.slice(1)} Cards
                </div>
                <CardDisplay cards={result.cards} config={result.config} />
              </div>
            ))}
          </div>
        )}

        {jobSuggestionResults.length > 0 && (
          <JobSuggestions
            results={jobSuggestionResults}
            onApplyJob={handleApplyJob}
            searchMetadata={jobSearchMetadata}
          />
        )}


        {chartResults.length > 0 && (
          <div className="space-y-4">
            {chartResults.map((chart, index) => (
              <ChartDisplay key={`${chart.id}-${index}`} chart={chart} />
            ))}
          </div>
        )}

        {!contentNullOrEmpty && (
          <MessageContent
            className={cn(
              "prose dark:prose-invert relative min-w-full bg-transparent p-0",
              "prose-h1:scroll-m-20 prose-h1:text-2xl prose-h1:font-semibold prose-h2:mt-8 prose-h2:scroll-m-20 prose-h2:text-xl prose-h2:mb-3 prose-h2:font-medium prose-h3:scroll-m-20 prose-h3:text-base prose-h3:font-medium prose-h4:scroll-m-20 prose-h5:scroll-m-20 prose-h6:scroll-m-20 prose-strong:font-medium prose-table:block prose-table:overflow-y-auto"
            )}
            markdown={true}
          >
            {children}
          </MessageContent>
        )}

        {sources && sources.length > 0 && <SourcesList sources={sources} />}

        {!isLastStreaming && !contentNullOrEmpty && (
          <MessageActions className="-ml-2 flex gap-0 opacity-0 transition-opacity group-hover:opacity-100">
            <MessageAction tooltip={copied ? "Copied!" : "Copy text"} side="bottom">
              <button
                className="hover:bg-accent/60 text-muted-foreground hover:text-foreground flex size-7.5 items-center justify-center rounded-full bg-transparent transition"
                aria-label="Copy text"
                onClick={copyToClipboard}
                type="button"
              >
                {copied ? <Check className="size-4" /> : <Copy className="size-4" />}
              </button>
            </MessageAction>

            {isLast && (
              <MessageAction tooltip="Regenerate" side="bottom" delayDuration={0}>
                <button
                  className="hover:bg-accent/60 text-muted-foreground hover:text-foreground flex size-7.5 items-center justify-center rounded-full bg-transparent transition"
                  aria-label="Regenerate"
                  onClick={onReload}
                  type="button"
                >
                  <ArrowClockwise className="size-4" />
                </button>
              </MessageAction>
            )}
          </MessageActions>
        )}

        {isQuoteEnabled && selectionInfo && selectionInfo.messageId && (
          <QuoteButton
            mousePosition={selectionInfo.position}
            onQuote={handleQuoteBtnClick}
            messageContainerRef={messageRef}
            onDismiss={clearSelection}
          />
        )}
      </div>
    </Message>
  );
}
