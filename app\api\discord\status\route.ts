import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE!
)

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const userId = searchParams.get('userId')

  if (!userId) {
    return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
  }

  try {
    // Check if user has Discord integration
    const { data: discordIntegration, error } = await supabase
      .from('discord_integrations')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error || !discordIntegration) {
      return NextResponse.json({
        linked: false,
        loginUrl: `/api/discord/login?userId=${userId}`
      })
    }

    // Check if token is expired and refresh if needed
    if (discordIntegration.expires_at && new Date() > new Date(discordIntegration.expires_at)) {
      try {
        const refreshResponse = await fetch('https://discord.com/api/oauth2/token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            client_id: process.env.DISCORD_CLIENT_ID!,
            client_secret: process.env.DISCORD_CLIENT_SECRET!,
            grant_type: 'refresh_token',
            refresh_token: discordIntegration.refresh_token,
          }),
        })

        if (refreshResponse.ok) {
          const tokens = await refreshResponse.json()
          
          // Update tokens in database
          const { error: updateError } = await supabase
            .from('discord_integrations')
            .update({
              access_token: tokens.access_token,
              refresh_token: tokens.refresh_token,
              expires_at: new Date(Date.now() + tokens.expires_in * 1000).toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq('user_id', userId)

          if (!updateError) {
            discordIntegration.access_token = tokens.access_token
          }
        } else {
          // Refresh failed, user needs to re-authenticate
          return NextResponse.json({
            linked: false,
            error: 'Authentication expired. Please reconnect your Discord account.',
            loginUrl: `/api/discord/login?userId=${userId}`
          })
        }
      } catch (error) {
        console.error('Error refreshing Discord token:', error)
        return NextResponse.json({
          linked: false,
          error: 'Authentication expired. Please reconnect your Discord account.',
          loginUrl: `/api/discord/login?userId=${userId}`
        })
      }
    }

    // Get current user info and activity from Discord
    let currentActivity = null
    let activityType = null
    let username = null
    let isOnline = false

    try {
      // Get user info
      const userResponse = await fetch('https://discord.com/api/users/@me', {
        headers: {
          'Authorization': `Bearer ${discordIntegration.access_token}`,
        },
      })

      if (userResponse.ok) {
        const userData = await userResponse.json()
        username = userData.username
      }

      return NextResponse.json({
        linked: true,
        username: username || discordIntegration.username,
        currentActivity,
        activityType,
        isOnline,
      })

    } catch (error) {
      console.error('Error fetching Discord activity:', error)
      return NextResponse.json({
        linked: true,
        username: discordIntegration.username,
        error: 'Could not fetch current activity'
      })
    }

  } catch (error) {
    console.error('Error checking Discord status:', error)
    return NextResponse.json(
      { error: 'Failed to check Discord status' },
      { status: 500 }
    )
  }
}
