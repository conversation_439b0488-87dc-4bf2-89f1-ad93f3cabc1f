"use client"

import { use<PERSON>ffe<PERSON>, use<PERSON><PERSON><PERSON>, useState } from "react"
import { usePathname, useRouter, useSearchParams } from "next/navigation"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>etContent, SheetHeader, SheetTitle, SheetDescription, SheetFooter } from "@/components/ui/sheet"
import Link from "next/link"
import { createClient } from "@/lib/supabase/client"
import { LayoutApp } from "../components/layout/layout-app"
import { MessagesProvider } from "@/lib/chat-store/messages/provider"
import { MoveLeft } from "lucide-react"

type Article = {
  id: string
  title: string
  summary: string
  content: string
  source: string
  date: string
  tags: string[]
  imageAlt: string
  image: string
}

const CATEGORIES = ["All", "AI", "Science", "Space", "Climate", "Healthcare", "Sports", "Technology"]

function slugify(s: string) {
  return s.toLowerCase().replace(/[^a-z0-9]+/g, "-").replace(/(^-|-$)/g, "")
}

export default function Page() {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const [articles, setArticles] = useState<Article[]>([])
  const [query, setQuery] = useState("")
  const [category, setCategory] = useState("All")
  const [open, setOpen] = useState(false)
  const [selectedId, setSelectedId] = useState<string | null>(null)

  // Map articles by id for quick lookup
  const byId = useMemo(() => {
    const map = new Map<string, Article>()
    for (const a of articles) map.set(a.id, a)
    return map
  }, [articles])

  // Filter articles by search query and category
  const filtered = useMemo(() => {
    const q = query.trim().toLowerCase()
    return articles.filter((a) => {
      const matchesQuery =
        !q ||
        a.title.toLowerCase().includes(q) ||
        a.summary.toLowerCase().includes(q) ||
        a.tags.join(" ").toLowerCase().includes(q)

      const matchesCategory = category === "All" || a.tags.some((t) => t.toLowerCase() === category.toLowerCase())
      return matchesQuery && matchesCategory
    })
  }, [articles, query, category])

  // Fetch articles from Supabase
  useEffect(() => {
    const supabase = createClient()
    if (!supabase) return

    const fetchArticles = async () => {
      const { data, error } = await supabase
        .from<Article>("articles")
        .select("*")
        .order("date", { ascending: false })

      if (error) console.error("Error fetching articles:", error)
      else setArticles(data || [])
    }

    fetchArticles()
  }, [])

  // Sync the detail sheet with ?q= param
  useEffect(() => {
    const qParam = searchParams.get("q")
    if (!qParam) {
      setOpen(false)
      setSelectedId(null)
      return
    }

    const byExactId = byId.get(qParam)
    const byTitleSlug = articles.find((a) => slugify(a.title) === qParam)
    const special = qParam === "aboutthenews" ? articles[0] : undefined
    const target = byExactId || byTitleSlug || special

    if (target) {
      setSelectedId(target.id)
      setOpen(true)
    } else {
      setOpen(false)
      setSelectedId(null)
    }
  }, [searchParams, byId, articles])

  const openArticle = (a: Article, replace?: boolean) => {
    const next = new URLSearchParams(searchParams.toString())
    next.set("q", a.id)
    setSelectedId(a.id)
    setOpen(true)
    const url = `${pathname}?${next.toString()}`
    if (replace) router.replace(url, { scroll: false })
    else router.push(url, { scroll: false })
  }

  const closeArticle = () => {
    const next = new URLSearchParams(searchParams.toString())
    next.delete("q")
    setOpen(false)
    setSelectedId(null)
    const base = next.toString()
    router.push(base ? `${pathname}?${base}` : pathname, { scroll: false })
  }

  const selected = selectedId ? (byId.get(selectedId) ?? null) : null

  return (
    <>
      <div className="absolute top-3 left-3 z-50">
        <Link
          href="/"
          className="flex items-center gap-2 rounded-lg 
             bg-white/80 px-3 py-1.5 text-sm font-medium shadow 
             hover:bg-white transition 
             dark:bg-zinc-900/80 dark:hover:bg-zinc-900 dark:text-zinc-100"
        >
          <MoveLeft className="h-4 w-4" />
          Back to Chat
        </Link>

      </div>

      <main className="mx-auto max-w-7xl w-full px-4 py-6">
        {/* Hero + Filters */}
        <section className="mb-6">
          <div className="mb-3">
            <h1 className="text-pretty text-2xl font-semibold md:text-3xl">Discover what’s new and worth knowing</h1>
            <p className="text-muted-foreground">Curated insights across AI, science, climate, space, and more.</p>
          </div>

          <div className="flex flex-col gap-3 md:flex-row md:items-center">
            <div className="flex-1">
              <Input
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="What do you want to learn?"
                aria-label="Search articles"
              />
            </div>
            <div className="flex flex-wrap gap-2">
              {CATEGORIES.map((c) => (
                <Badge
                  key={c}
                  variant={c === category ? "default" : "secondary"}
                  className="cursor-pointer"
                  role="button"
                  tabIndex={0}
                  onClick={() => setCategory(c)}
                  onKeyDown={(e) => e.key === "Enter" && setCategory(c)}
                  aria-label={`Filter by ${c}`}
                >
                  {c}
                </Badge>
              ))}
            </div>
          </div>
        </section>

        {/* Grid of articles */}
        <section aria-live="polite" className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {filtered.map((a) => (
            <Card key={a.id} className="transition hover:bg-accent/30 p-0" role="article">
              <button onClick={() => openArticle(a)} className="text-left" aria-label={`Open details for ${a.title}`}>
                <CardHeader className="p-0">
                  <img
                    src={a.image || "/placeholder.svg?height=160&width=320&query=news%20image"}
                    alt={a.imageAlt}
                    className="h-50 w-full rounded-t object-cover"
                  />
                </CardHeader>
                <CardContent className="p-4">
                  <div className="mb-2 flex flex-wrap gap-1">
                    {a.tags.slice(0, 3).map((t) => (
                      <Badge key={t} variant="outline">
                        {t}
                      </Badge>
                    ))}
                  </div>
                  <CardTitle className="mb-1 text-base md:text-lg">{a.title}</CardTitle>
                  <CardDescription className="line-clamp-2">{a.summary}</CardDescription>
                  <div className="mt-3 flex items-center justify-between text-xs text-muted-foreground">
                    <span>{a.source}</span>
                    <span>{new Date(a.date).toLocaleDateString()}</span>
                  </div>
                </CardContent>
              </button>

              <div className="flex items-center justify-end gap-2 px-4 pb-4">
                <Button variant="ghost" size="sm" onClick={() => openArticle(a)} aria-label={`Open sheet for ${a.title}`}>
                  Open
                </Button>
                <Button
                  variant="link"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation(); // Prevents bubbling
                    const next = new URLSearchParams(searchParams.toString()); // Copy current query params
                    next.set("q", (a.title)); // Set the query param
                    const url = `/?${next.toString()}`; // Construct full URL
                    window.open(url, "_blank"); // Open in a new tab
                  }}
                  aria-label="Know more via /?q=aboutthenews"
                  title="Know more (/ ?q=aboutthenews)"
                >
                  Know more
                </Button>

              </div>
            </Card>
          ))}
        </section>

        {/* Detail Sheet synced with ?q= */}
        {/* Detail Sheet synced with ?q= */}
        <Sheet open={open} onOpenChange={(nextOpen) => !nextOpen && closeArticle()}>
          <SheetContent className="flex max-w-[30rem] h-full flex-col p-3 overflow-y-auto">
            <SheetHeader>
              <SheetTitle className="text-pretty">{selected?.title ?? "Loading..."}</SheetTitle>
              <SheetDescription>
                {selected ? (
                  <>
                    <span className="sr-only">Source</span>
                    <span>{selected.source}</span> • <span className="sr-only">Date</span>
                    <span>{selected.date ? new Date(selected.date).toLocaleDateString() : ""}</span>
                  </>
                ) : (
                  "Fetching article details"
                )}
              </SheetDescription>
            </SheetHeader>

            {selected && (
              <div className="mt-4 flex flex-col gap-3">
                <img
                  src={selected.image || "/placeholder.svg?height=160&width=320&query=news%20image"}
                  alt={selected.imageAlt}
                  className="h-[15rem] w-full rounded object-cover"
                />
                <div className="flex flex-wrap gap-1">
                  {selected.tags.map((t) => (
                    <Badge key={t} variant="outline">
                      {t}
                    </Badge>
                  ))}
                </div>
                <p className="text-sm leading-relaxed text-muted-foreground">{selected.summary}</p>
                <p className="text-sm leading-relaxed">{selected.content}</p>
              </div>
            )}

            <div className="mt-auto" />
            <SheetFooter className="mt-6 flex items-center justify-between flex-row">
              <Button variant="secondary" onClick={closeArticle}>
                Close
              </Button>
              {selected && (
                <Button asChild aria-label="Open deep link" title="Know More">
                  <Link href={`/?q=${encodeURIComponent(selected.title)}`} target="_blank">
                    Know More
                  </Link>
                </Button>
              )}
            </SheetFooter>
          </SheetContent>
        </Sheet>


      </main></>
  )
}