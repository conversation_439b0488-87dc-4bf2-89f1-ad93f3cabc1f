"use client"

import { useState, useEffect } from "react"
import { useUser } from "@supabase/auth-helpers-react"
import { But<PERSON> } from "@/components/ui/button"
import { DrawerClose } from "@/components/ui/drawer"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { isSupabaseEnabled } from "@/lib/supabase/config"
import { supabase } from "@/lib/supabase/config" // Import supabase client from your config
import { cn, isDev } from "@/lib/utils"
import {
  GearSixIcon,
  PaintBrushIcon,
  XIcon,
  ListChecksIcon,
  BellIcon,
  ArchiveBoxIcon,
  EnvelopeIcon,
  UsersThreeIcon,
  ChartBarIcon,
} from "@phosphor-icons/react"
import { SMTPSection } from "./stmp/stmp-details"
import { InteractionPreferences } from "./appearance/interaction-preferences"
import { LayoutSettings } from "./appearance/layout-settings"
import { ThemeSelection } from "./appearance/theme-selection"
import { ConnectionsPlaceholder } from "./connections/connections-placeholder"
import { DeveloperTools } from "./connections/developer-tools"
import { OllamaSection } from "./connections/ollama-section"
import { AccountManagement } from "./general/account-management"
import { UserProfile } from "./general/user-profile"
import { ModelsSettings } from "./models/models-settings"
import { TaskPreferences } from "./appearance/task-preferences"
import { ReminderPreferences } from "./appearance/reminder-preferences"
import { MemoryPreferences } from "./appearance/memory-preferences"

type SettingsContentProps = {
  isDrawer?: boolean
}

type TabType =
  | "general"
  | "appearance"
  | "models"
  | "connections"
  | "tasks"
  | "reminder"
  | "memory"
  | "smtp"
  | "school-management"
  | "school-analytics"

async function checkUserIsSchool(userId: string, supabaseClient: any): Promise<boolean> {
  try {
    if (!supabaseClient) {
      console.error('Supabase client not available');
      return false;
    }

    const { data, error } = await supabaseClient
      .from('users')
      .select('isschool')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching user school status:', error.message);
      return false;
    }

    return data?.isschool === true;
  } catch (error: any) {
    console.error('Exception checking user school status:', error);
    return false;
  }
}

export function SettingsContent({ isDrawer = false }: SettingsContentProps) {
  const [activeTab, setActiveTab] = useState<TabType>("general")
  const [isSchool, setIsSchool] = useState<boolean>(false)
  const [loading, setLoading] = useState(true)

  const { user } = useUser()

  useEffect(() => {
    const fetchSchoolStatus = async () => {
      if (!user?.id || !isSupabaseEnabled) {
        setLoading(false)
        return
      }

      try {
        const schoolStatus = await checkUserIsSchool(user.id, supabase)
        setIsSchool(schoolStatus)
      } catch (error) {
        console.error('Error checking school status:', error)
        setIsSchool(false)
      } finally {
        setLoading(false)
      }
    }

    fetchSchoolStatus()
  }, [user?.id])

  return (
    <div
      className={cn(
        "flex w-full flex-col overflow-y-auto",
        isDrawer ? "p-0 pb-16" : "py-0"
      )}
    >
      {isDrawer && (
        <div className="border-border mb-2 flex items-center justify-between border-b px-4 pb-2">
          <h2 className="text-lg font-medium">Settings</h2>
          <DrawerClose asChild>
            <Button variant="ghost" size="icon">
              <XIcon className="size-4" />
            </Button>
          </DrawerClose>
        </div>
      )}

      <Tabs
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as TabType)}
        className={cn("flex w-full flex-row", isDrawer ? "" : "flex min-h-[400px]")}
      >
        {isDrawer ? (
          // 📱 Mobile version
          <div className="w-full items-start justify-start overflow-hidden py-4">
            <div>
              <TabsList className="mb-4 flex w-full min-w-0 flex-nowrap items-center justify-start overflow-x-auto bg-transparent px-0">
                <TabsTrigger value="general" className="ml-6 flex shrink-0 items-center gap-2">
                  <GearSixIcon className="size-4" />
                  <span>General</span>
                </TabsTrigger>

                <TabsTrigger value="appearance" className="flex shrink-0 items-center gap-2">
                  <PaintBrushIcon className="size-4" />
                  <span>Appearance</span>
                </TabsTrigger>

                <TabsTrigger value="smtp" className="flex shrink-0 items-center gap-2">
                  <EnvelopeIcon className="size-4" />
                  <span>SMTP</span>
                </TabsTrigger>

                <TabsTrigger value="tasks" className="flex shrink-0 items-center gap-2">
                  <ListChecksIcon className="size-4" />
                  <span>Tasks</span>
                </TabsTrigger>

                <TabsTrigger value="reminder" className="flex shrink-0 items-center gap-2">
                  <BellIcon className="size-4" />
                  <span>Reminder</span>
                </TabsTrigger>

                <TabsTrigger value="memory" className="flex shrink-0 items-center gap-2">
                  <ArchiveBoxIcon className="size-4" />
                  <span>Memory</span>
                </TabsTrigger>

                {/* School tabs - only show when user is school and not loading */}
                {!loading && isSchool && (
                  <>
                    <TabsTrigger value="school-management" className="flex shrink-0 items-center gap-2">
                      <UsersThreeIcon className="size-4" />
                      <span>Students</span>
                    </TabsTrigger>

                    <TabsTrigger value="school-analytics" className="flex shrink-0 items-center gap-2">
                      <ChartBarIcon className="size-4" />
                      <span>Analytics</span>
                    </TabsTrigger>
                  </>
                )}
              </TabsList>
            </div>

            {/* 📱 Mobile Content */}
            <TabsContent value="general" className="space-y-6 px-6">
              <UserProfile />
              {isSupabaseEnabled && <AccountManagement />}
            </TabsContent>

            <TabsContent value="appearance" className="space-y-6 px-6">
              <ThemeSelection />
              <LayoutSettings />
              <InteractionPreferences />
            </TabsContent>

            <TabsContent value="smtp" className="px-6">
              <SMTPSection />
            </TabsContent>

            <TabsContent value="models" className="px-6">
              <ModelsSettings />
            </TabsContent>

            <TabsContent value="connections" className="space-y-6 px-6">
              {!isDev && <ConnectionsPlaceholder />}
              {isDev && <OllamaSection />}
              {isDev && <DeveloperTools />}
            </TabsContent>

            <TabsContent value="tasks" className="space-y-6 px-6">
              <TaskPreferences />
            </TabsContent>

            <TabsContent value="reminder" className="space-y-6 px-6">
              <ReminderPreferences />
            </TabsContent>

            <TabsContent value="memory" className="space-y-6 px-6">
              <MemoryPreferences />
            </TabsContent>

            {/* School-only content */}
            {isSchool && (
              <>
                <TabsContent value="school-management" className="space-y-6 px-6">
                  <div>School Management Content</div>
                </TabsContent>

                <TabsContent value="school-analytics" className="space-y-6 px-6">
                  <div>School Analytics Content</div>
                </TabsContent>
              </>
            )}
          </div>
        ) : (
          // 🖥️ Desktop version
          <>
            <TabsList className="block w-48 rounded-none bg-transparent px-3 pt-4">
              <div className="flex w-full flex-col gap-1">
                <TabsTrigger value="general" className="w-full justify-start rounded-md px-3 py-2 text-left">
                  <div className="flex items-center gap-2">
                    <GearSixIcon className="size-4" />
                    <span>General</span>
                  </div>
                </TabsTrigger>

                <TabsTrigger value="appearance" className="w-full justify-start rounded-md px-3 py-2 text-left">
                  <div className="flex items-center gap-2">
                    <PaintBrushIcon className="size-4" />
                    <span>Appearance</span>
                  </div>
                </TabsTrigger>

                <TabsTrigger value="smtp" className="w-full justify-start rounded-md px-3 py-2 text-left">
                  <div className="flex items-center gap-2">
                    <EnvelopeIcon className="size-4" />
                    <span>SMTP</span>
                  </div>
                </TabsTrigger>

                <TabsTrigger value="tasks" className="w-full justify-start rounded-md px-3 py-2 text-left">
                  <div className="flex items-center gap-2">
                    <ListChecksIcon className="size-4" />
                    <span>Tasks</span>
                  </div>
                </TabsTrigger>

                <TabsTrigger value="memory" className="w-full justify-start rounded-md px-3 py-2 text-left">
                  <div className="flex items-center gap-2">
                    <ArchiveBoxIcon className="size-4" />
                    <span>Memory</span>
                  </div>
                </TabsTrigger>

                {/* School tabs - only show when user is school and not loading */}
                {!loading && isSchool && (
                  <>
                    <TabsTrigger value="school-management" className="w-full justify-start rounded-md px-3 py-2 text-left">
                      <div className="flex items-center gap-2">
                        <UsersThreeIcon className="size-4" />
                        <span>Students</span>
                      </div>
                    </TabsTrigger>

                    <TabsTrigger value="school-analytics" className="w-full justify-start rounded-md px-3 py-2 text-left">
                      <div className="flex items-center gap-2">
                        <ChartBarIcon className="size-4" />
                        <span>Analytics</span>
                      </div>
                    </TabsTrigger>
                  </>
                )}
              </div>
            </TabsList>

            {/* 🖥️ Desktop Content */}
            <div className="flex-1 overflow-auto px-6 pt-4">
              <TabsContent value="general" className="mt-0 space-y-6">
                <UserProfile />
                {isSupabaseEnabled && <AccountManagement />}
              </TabsContent>

              <TabsContent value="appearance" className="mt-0 space-y-6">
                <ThemeSelection />
                <LayoutSettings />
                <InteractionPreferences />
              </TabsContent>

              <TabsContent value="smtp" className="mt-0 space-y-6">
                <SMTPSection />
              </TabsContent>

              <TabsContent value="models" className="mt-0 space-y-6">
                <ModelsSettings />
              </TabsContent>

              <TabsContent value="connections" className="mt-0 space-y-6">
                {!isDev && <ConnectionsPlaceholder />}
                {isDev && <OllamaSection />}
                {isDev && <DeveloperTools />}
              </TabsContent>

              <TabsContent value="tasks" className="mt-0 space-y-6">
                <TaskPreferences />
              </TabsContent>

              <TabsContent value="reminder" className="mt-0 space-y-6">
                <ReminderPreferences />
              </TabsContent>

              <TabsContent value="memory" className="mt-0 space-y-6">
                <MemoryPreferences />
              </TabsContent>

              {/* School-only content */}
              {isSchool && (
                <>
                  <TabsContent value="school-management" className="mt-0 space-y-6">
                    <div>School Management Content</div>
                  </TabsContent>

                  <TabsContent value="school-analytics" className="mt-0 space-y-6">
                    <div>School Analytics Content</div>
                  </TabsContent>
                </>
              )}
            </div>
          </>
        )}
      </Tabs>
    </div>
  )
}
