import { openproviders } from "@/lib/openproviders"
import { ModelConfig } from "../types"

const vercelModels: ModelConfig[] = [
  {
    id: "openai/gpt-4o",
    name: "GPT-4o",
    provider: "Vercel AI Gateway",
    providerId: "vercel",
    baseProviderId: "openai",
    modelFamily: "GPT-4",
    description: "OpenAI's flagship multimodal model via Vercel AI Gateway.",
    tags: ["multimodal", "vision", "flagship", "gateway"],
    contextWindow: 128000,
    inputCost: 2.5,
    outputCost: 10.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://vercel.com/docs/ai-gateway",
    apiDocs: "https://vercel.com/docs/ai-gateway/authentication",
    modelPage: "https://openai.com/gpt-4",
    releasedAt: "2024-05-13",
    icon: "vercel",
    apiSdk: (apiKey?: string) =>
      openproviders("openai/gpt-4o", "vercel", apiKey),
  },
  {
    id: "openai/gpt-4o-mini",
    name: "GPT-4o Mini",
    provider: "Vercel AI Gateway",
    providerId: "vercel",
    baseProviderId: "openai",
    modelFamily: "GPT-4",
    description: "Cost-efficient GPT-4 variant via Vercel AI Gateway.",
    tags: ["efficient", "vision", "gateway", "cost-effective"],
    contextWindow: 128000,
    inputCost: 0.15,
    outputCost: 0.6,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Fast",
    intelligence: "High",
    website: "https://vercel.com/docs/ai-gateway",
    apiDocs: "https://vercel.com/docs/ai-gateway/authentication",
    modelPage: "https://openai.com/gpt-4",
    releasedAt: "2024-07-18",
    icon: "vercel",
    apiSdk: (apiKey?: string) =>
      openproviders("openai/gpt-4o-mini", "vercel", apiKey),
  },
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    provider: "Vercel AI Gateway",
    providerId: "vercel",
    baseProviderId: "anthropic",
    modelFamily: "Claude 3.5",
    description: "Anthropic's powerful Claude model via Vercel AI Gateway.",
    tags: ["reasoning", "analysis", "gateway", "flagship"],
    contextWindow: 200000,
    inputCost: 3.0,
    outputCost: 15.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://vercel.com/docs/ai-gateway",
    apiDocs: "https://vercel.com/docs/ai-gateway/authentication",
    modelPage: "https://www.anthropic.com/claude",
    releasedAt: "2024-06-20",
    icon: "vercel",
    apiSdk: (apiKey?: string) =>
      openproviders("anthropic/claude-3.5-sonnet", "vercel", apiKey),
  },
  {
    id: "anthropic/claude-3-haiku",
    name: "Claude 3 Haiku",
    provider: "Vercel AI Gateway",
    providerId: "vercel",
    baseProviderId: "anthropic",
    modelFamily: "Claude 3",
    description: "Fast and efficient Claude model via Vercel AI Gateway.",
    tags: ["fast", "efficient", "gateway", "cost-effective"],
    contextWindow: 200000,
    inputCost: 0.25,
    outputCost: 1.25,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: false,
    openSource: false,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://vercel.com/docs/ai-gateway",
    apiDocs: "https://vercel.com/docs/ai-gateway/authentication",
    modelPage: "https://www.anthropic.com/claude",
    releasedAt: "2024-03-07",
    icon: "vercel",
    apiSdk: (apiKey?: string) =>
      openproviders("anthropic/claude-3-haiku", "vercel", apiKey),
  },
  {
    id: "google/gemini-pro-1.5",
    name: "Gemini Pro 1.5",
    provider: "Vercel AI Gateway",
    providerId: "vercel",
    baseProviderId: "google",
    modelFamily: "Gemini",
    description: "Google's advanced Gemini model via Vercel AI Gateway.",
    tags: ["multimodal", "vision", "gateway", "large-context"],
    contextWindow: 1000000,
    inputCost: 1.25,
    outputCost: 5.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://vercel.com/docs/ai-gateway",
    apiDocs: "https://vercel.com/docs/ai-gateway/authentication",
    modelPage: "https://ai.google.dev/gemini-api",
    releasedAt: "2024-02-15",
    icon: "vercel",
    apiSdk: (apiKey?: string) =>
      openproviders("google/gemini-pro-1.5", "vercel", apiKey),
  },
  {
    id: "google/gemini-flash-1.5",
    name: "Gemini Flash 1.5",
    provider: "Vercel AI Gateway",
    providerId: "vercel",
    baseProviderId: "google",
    modelFamily: "Gemini",
    description: "Fast and efficient Gemini variant via Vercel AI Gateway.",
    tags: ["fast", "efficient", "vision", "gateway"],
    contextWindow: 1000000,
    inputCost: 0.075,
    outputCost: 0.3,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: false,
    openSource: false,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://vercel.com/docs/ai-gateway",
    apiDocs: "https://vercel.com/docs/ai-gateway/authentication",
    modelPage: "https://ai.google.dev/gemini-api",
    releasedAt: "2024-05-14",
    icon: "vercel",
    apiSdk: (apiKey?: string) =>
      openproviders("google/gemini-flash-1.5", "vercel", apiKey),
  },
  {
    id: "mistral/mistral-large",
    name: "Mistral Large",
    provider: "Vercel AI Gateway",
    providerId: "vercel",
    baseProviderId: "mistral",
    modelFamily: "Mistral",
    description: "Mistral's flagship model via Vercel AI Gateway.",
    tags: ["reasoning", "flagship", "gateway", "multilingual"],
    contextWindow: 128000,
    inputCost: 2.0,
    outputCost: 6.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://vercel.com/docs/ai-gateway",
    apiDocs: "https://vercel.com/docs/ai-gateway/authentication",
    modelPage: "https://mistral.ai/news/mistral-large/",
    releasedAt: "2024-02-26",
    icon: "vercel",
    apiSdk: (apiKey?: string) =>
      openproviders("mistral/mistral-large", "vercel", apiKey),
  },
  {
    id: "xai/grok-beta",
    name: "Grok Beta",
    provider: "Vercel AI Gateway",
    providerId: "vercel",
    baseProviderId: "xai",
    modelFamily: "Grok",
    description: "xAI's Grok model via Vercel AI Gateway.",
    tags: ["reasoning", "gateway", "beta", "real-time"],
    contextWindow: 131072,
    inputCost: 5.0,
    outputCost: 15.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://vercel.com/docs/ai-gateway",
    apiDocs: "https://vercel.com/docs/ai-gateway/authentication",
    modelPage: "https://x.ai/",
    releasedAt: "2024-12-07",
    icon: "vercel",
    apiSdk: (apiKey?: string) =>
      openproviders("xai/grok-beta", "vercel", apiKey),
  },
  {
    id: "perplexity/llama-3.1-sonar-large",
    name: "Llama 3.1 Sonar Large",
    provider: "Vercel AI Gateway",
    providerId: "vercel",
    baseProviderId: "perplexity",
    modelFamily: "Sonar",
    description: "Perplexity's search-optimized model via Vercel AI Gateway.",
    tags: ["search", "real-time", "gateway", "web-access"],
    contextWindow: 127072,
    inputCost: 1.0,
    outputCost: 1.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://vercel.com/docs/ai-gateway",
    apiDocs: "https://vercel.com/docs/ai-gateway/authentication",
    modelPage: "https://www.perplexity.ai/",
    releasedAt: "2024-08-01",
    icon: "vercel",
    apiSdk: (apiKey?: string) =>
      openproviders("perplexity/llama-3.1-sonar-large", "vercel", apiKey),
  },
  {
    id: "deepseek/deepseek-v3",
    name: "DeepSeek V3",
    provider: "Vercel AI Gateway",
    providerId: "vercel",
    baseProviderId: "deepseek",
    modelFamily: "DeepSeek",
    description: "DeepSeek's powerful reasoning model via Vercel AI Gateway.",
    tags: ["reasoning", "gateway", "cost-effective", "open-source"],
    contextWindow: 65536,
    inputCost: 0.27,
    outputCost: 1.1,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: true,
    speed: "Medium",
    intelligence: "High",
    website: "https://vercel.com/docs/ai-gateway",
    apiDocs: "https://vercel.com/docs/ai-gateway/authentication",
    modelPage: "https://www.deepseek.com/",
    releasedAt: "2024-12-26",
    icon: "vercel",
    apiSdk: (apiKey?: string) =>
      openproviders("deepseek/deepseek-v3", "vercel", apiKey),
  }
]

export { vercelModels }
