import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get('userId');

    if (!userId) {
      return new Response('User ID required', { status: 400 });
    }

    const clientId = process.env.NOTION_CLIENT_ID;
    
    // Dynamic redirect URI based on environment
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 
                   process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : 
                   'https://ai.nityasha.com';
    
    const redirectUri = `${baseUrl}/api/notion/callback`;
    
    console.log('Redirect URI:', redirectUri); // Debug log
    
    if (!clientId) {
      return new Response('Notion client ID not configured', { status: 500 });
    }

    const authUrl = new URL('https://api.notion.com/v1/oauth/authorize');
    authUrl.searchParams.set('client_id', clientId);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('owner', 'user');
    authUrl.searchParams.set('state', userId);

    console.log('Auth URL:', authUrl.toString()); // Debug log

    return Response.redirect(authUrl.toString());
  } catch (error) {
    console.error('Notion login error:', error);
    return new Response('Login failed', { status: 500 });
  }
}
