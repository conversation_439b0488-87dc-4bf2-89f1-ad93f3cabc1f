// components/ui/chart-display.tsx
"use client"

import { TrendingDown, TrendingUp } from "lucide-react"
import { 
  Bar, 
  BarChart, 
  Line, 
  LineChart, 
  Area, 
  AreaChart, 
  Pie, 
  PieChart, 
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
} from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart"

type ChartData = {
  label: string;
  value: number;
  category?: string;
}

type ChartDisplayConfig = {
  id: string;
  type: 'bar' | 'line' | 'pie' | 'area' | 'donut';
  title: string;
  description?: string;
  data: ChartData[];
  options: {
    xAxisLabel?: string;
    yAxisLabel?: string;
    colors?: string[];
    showLegend?: boolean;
    showDataLabels?: boolean;
    showGrid?: boolean;
  };
  metadata: {
    totalDataPoints: number;
    dataRange: { min: number; max: number };
    categories: string[];
  };
}

interface ChartDisplayProps {
  chart: ChartDisplayConfig;
}

export function ChartDisplay({ chart }: ChartDisplayProps) {
  // Check if dark mode is active
  const isDark = typeof window !== 'undefined' && 
    document.documentElement.classList.contains('dark')

  // Transform data for recharts format
  const chartData = chart.data.map((item, index) => ({
    name: item.label,
    value: item.value,
    fill: `hsl(var(--chart-${(index % 5) + 1}))`,
    category: item.category
  }));

  // Create chart configuration
  const chartConfig = chart.data.reduce((config, item, index) => {
    const key = item.label.toLowerCase().replace(/\s+/g, '');
    config[key] = {
      label: item.label,
      color: `hsl(var(--chart-${(index % 5) + 1}))`,
      icon: index % 2 === 0 ? TrendingUp : TrendingDown,
    };
    return config;
  }, {} as ChartConfig);

  // Add main value to config
  chartConfig.value = {
    label: chart.options.yAxisLabel || "Value",
    color: "hsl(var(--chart-1))",
    icon: TrendingUp,
  };

  const renderChart = () => {
    const commonProps = {
      accessibilityLayer: true,
      data: chartData,
      margin: { left: 12, right: 12 }
    };

    switch (chart.type) {
      case 'bar':
        return (
          <BarChart {...commonProps}>
            {chart.options.showGrid && (
              <CartesianGrid 
                vertical={false} 
                className="stroke-border dark:stroke-border"
                strokeDasharray="3 3"
              />
            )}
            <XAxis 
              dataKey="name" 
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              className="fill-muted-foreground text-xs"
              tickFormatter={(value) => value.slice(0, 10)}
            />
            <YAxis 
              tickLine={false}
              axisLine={false}
              className="fill-muted-foreground text-xs"
            />
            <ChartTooltip 
              cursor={{ 
                fill: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)' 
              }}
              content={<ChartTooltipContent indicator="line" />} 
            />
            <Bar 
              dataKey="value" 
              fill="var(--color-value)"
              radius={4}
              className="fill-primary dark:fill-primary"
            />
            {chart.options.showLegend && (
              <ChartLegend content={<ChartLegendContent />} />
            )}
          </BarChart>
        );

      case 'line':
        return (
          <LineChart {...commonProps}>
            {chart.options.showGrid && (
              <CartesianGrid 
                vertical={false} 
                className="stroke-border dark:stroke-border"
                strokeDasharray="3 3"
              />
            )}
            <XAxis 
              dataKey="name" 
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              className="fill-muted-foreground text-xs"
              tickFormatter={(value) => value.slice(0, 10)}
            />
            <YAxis 
              tickLine={false}
              axisLine={false}
              className="fill-muted-foreground text-xs"
            />
            <ChartTooltip 
              cursor={false}
              content={<ChartTooltipContent indicator="line" />} 
            />
            <Line 
              dataKey="value" 
              type="natural" 
              stroke="var(--color-value)"
              strokeWidth={2}
              dot={{ 
                fill: "var(--color-value)", 
                strokeWidth: 2, 
                r: 4,
                className: "fill-primary dark:fill-primary"
              }}
              activeDot={{ 
                r: 6, 
                stroke: "var(--color-value)", 
                strokeWidth: 2,
                className: "fill-primary dark:fill-primary"
              }}
            />
            {chart.options.showLegend && (
              <ChartLegend content={<ChartLegendContent />} />
            )}
          </LineChart>
        );

      case 'area':
        return (
          <AreaChart {...commonProps}>
            {chart.options.showGrid && (
              <CartesianGrid 
                vertical={false} 
                className="stroke-border dark:stroke-border"
                strokeDasharray="3 3"
              />
            )}
            <XAxis 
              dataKey="name" 
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              className="fill-muted-foreground text-xs"
              tickFormatter={(value) => value.slice(0, 10)}
            />
            <YAxis 
              tickLine={false}
              axisLine={false}
              className="fill-muted-foreground text-xs"
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="line" />}
            />
            <Area
              dataKey="value"
              type="natural"
              fill="var(--color-value)"
              fillOpacity={0.4}
              stroke="var(--color-value)"
              strokeWidth={2}
              className="fill-primary/40 dark:fill-primary/30 stroke-primary dark:stroke-primary"
            />
            {chart.options.showLegend && (
              <ChartLegend content={<ChartLegendContent />} />
            )}
          </AreaChart>
        );

      case 'pie':
      case 'donut':
        const innerRadius = chart.type === 'donut' ? 60 : 0;
        return (
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie
              data={chartData}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              innerRadius={innerRadius}
              outerRadius={80}
              stroke="hsl(var(--background))"
              strokeWidth={2}
            >
              {chartData.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={entry.fill}
                  className={`fill-chart-${(index % 5) + 1}`}
                />
              ))}
            </Pie>
            {chart.options.showLegend && (
              <ChartLegend content={<ChartLegendContent />} />
            )}
          </PieChart>
        );

      default:
        return (
          <div className="text-muted-foreground dark:text-muted-foreground">
            Unsupported chart type
          </div>
        );
    }
  };

  // Calculate trend (simple example)
  const calculateTrend = () => {
    if (chart.data.length < 2) return { percentage: 0, isUp: true };
    const lastValue = chart.data[chart.data.length - 1].value;
    const prevValue = chart.data[chart.data.length - 2].value;
    const percentage = ((lastValue - prevValue) / prevValue * 100);
    return { 
      percentage: Math.abs(percentage).toFixed(1), 
      isUp: percentage > 0 
    };
  };

  const trend = calculateTrend();

  return (
    <Card className="bg-card dark:bg-card border-border dark:border-border transition-colors duration-200">
      <CardHeader>
        <CardTitle className="text-card-foreground dark:text-card-foreground">
          {chart.title}
        </CardTitle>
        {chart.description && (
          <CardDescription className="text-muted-foreground dark:text-muted-foreground">
            {chart.description}
          </CardDescription>
        )}
      </CardHeader>
      <CardContent className="bg-card dark:bg-card">
        <ChartContainer config={chartConfig}>
          {renderChart()}
        </ChartContainer>
      </CardContent>
      <CardFooter className="bg-card dark:bg-card">
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium text-muted-foreground dark:text-muted-foreground">
              Trending {trend.isUp ? 'up' : 'down'} by {trend.percentage}% 
              {trend.isUp ? (
                <TrendingUp className="h-4 w-4 text-green-500 dark:text-green-400" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 dark:text-red-400" />
              )}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}
