// /app/api/spotify/callback/route.ts
import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const code = searchParams.get('code');
  const state = searchParams.get('state'); // This contains the userId
  const error = searchParams.get('error');

  // Get the base URL from the request
  const baseUrl = new URL(request.url).origin;
  const redirectUrl = process.env.NEXT_PUBLIC_APP_URL || baseUrl;

  // Validate environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE;
  const spotifyClientId = process.env.SPOTIFY_CLIENT_ID;
  const spotifyClientSecret = process.env.SPOTIFY_CLIENT_SECRET;
  const spotifyRedirectUri = process.env.SPOTIFY_REDIRECT_URI;

  // Debug environment variables (remove in production)
  console.log('Environment check:', {
    supabaseUrl: !!supabaseUrl,
    supabaseKey: !!supabaseKey,
    spotifyClientId: !!spotifyClientId,
    spotifyClientSecret: !!spotifyClientSecret,
    spotifyRedirectUri: !!spotifyRedirectUri,
  });

  // Check for missing environment variables
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase configuration:', {
      url: !!supabaseUrl,
      key: !!supabaseKey
    });
    return NextResponse.redirect(new URL(`/?error=configuration_error`, redirectUrl));
  }

  if (!spotifyClientId || !spotifyClientSecret || !spotifyRedirectUri) {
    console.error('Missing Spotify configuration:', {
      clientId: !!spotifyClientId,
      clientSecret: !!spotifyClientSecret,
      redirectUri: !!spotifyRedirectUri
    });
    return NextResponse.redirect(new URL(`/?error=spotify_config_error`, redirectUrl));
  }

  // Initialize Supabase client with validated environment variables
  const supabase = createClient(supabaseUrl, supabaseKey);

  if (error) {
    console.error('Spotify auth error:', error);
    return NextResponse.redirect(new URL(`/?error=spotify_auth_failed`, redirectUrl));
  }

  if (!code || !state) {
    console.error('Missing parameters:', { code: !!code, state: !!state });
    return NextResponse.redirect(new URL(`/?error=missing_parameters`, redirectUrl));
  }

  const userId = state;

  try {
    console.log('Exchanging code for tokens...');
    
    // Exchange code for tokens
    const tokenResponse = await axios.post(
      'https://accounts.spotify.com/api/token',
      new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: spotifyRedirectUri,
        client_id: spotifyClientId,
        client_secret: spotifyClientSecret,
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    const {
      access_token,
      refresh_token,
      expires_in,
    } = tokenResponse.data;

    if (!access_token) {
      console.error('No access token received');
      return NextResponse.redirect(new URL(`/?error=token_exchange_failed`, redirectUrl));
    }

    console.log('Getting Spotify user profile...');

    // Get Spotify user profile
    const userResponse = await axios.get('https://api.spotify.com/v1/me', {
      headers: {
        Authorization: `Bearer ${access_token}`,
      },
    });

    const spotifyUser = userResponse.data;

    if (!spotifyUser || !spotifyUser.id) {
      console.error('Invalid Spotify user data');
      return NextResponse.redirect(new URL(`/?error=invalid_user_data`, redirectUrl));
    }

    console.log('Spotify user retrieved:', spotifyUser.id);

    // Calculate expiry time
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + expires_in);

    console.log('Saving to database...');

    // Insert or update Spotify account
    const { error: dbError } = await supabase
      .from('spotify_accounts')
      .upsert({
        user_id: userId,
        spotify_user_id: spotifyUser.id,
        access_token,
        refresh_token,
        expires_at: expiresAt.toISOString(),
        updated_at: new Date().toISOString(),
      });

    if (dbError) {
      console.error('Database error:', dbError);
      return NextResponse.redirect(new URL(`/?error=database_failed&details=${encodeURIComponent(dbError.message)}`, redirectUrl));
    }

    console.log('Spotify account saved successfully');

    // Redirect back to app with success
    return NextResponse.redirect(new URL(`/?spotify=connected&user=${spotifyUser.display_name || spotifyUser.id}`, redirectUrl));

  } catch (error: any) {
    console.error('Spotify callback error:', error);
    
    // Log more detailed error information
    if (error?.response) {
      console.error('Error response:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
      });
    }

    const errorMessage = error?.response?.data?.error_description || 
                        error?.response?.data?.error || 
                        error?.message || 
                        'unknown_error';

    return NextResponse.redirect(new URL(`/?error=spotify_callback_failed&details=${encodeURIComponent(errorMessage)}`, redirectUrl));
  }
}
