"use client"

import { cn } from "@/lib/utils"
import type { ToolInvocationUIPart } from "@ai-sdk/ui-utils"
import { CheckCircle, Code, Link, Wrench } from "@phosphor-icons/react"
import { AnimatePresence, motion } from "framer-motion"
import { useMemo, useState, useEffect } from "react"

// Import the Loader component from prompt-kit
// You'll need to install this: npx shadcn add "https://prompt-kit.com/c/loader.json"
import { Loader } from "@/components/prompt-kit/loader"

interface ToolInvocationProps {
  toolInvocations: ToolInvocationUIPart[]
  className?: string
  autoHideDelay?: number // Delay in ms before hiding completed tools
}

const TRANSITION = {
  type: "spring",
  duration: 0.3,
  bounce: 0,
}

export function ToolInvocation({
  toolInvocations,
  className,
  autoHideDelay = 2000, // Hide after 2 seconds by default
}: ToolInvocationProps) {
  const [isVisible, setIsVisible] = useState(true)
  
  const toolInvocationsData = Array.isArray(toolInvocations)
    ? toolInvocations
    : [toolInvocations]

  // Group tool invocations by toolCallId
  const groupedTools = toolInvocationsData.reduce(
    (acc, item) => {
      const { toolCallId } = item.toolInvocation
      if (!acc[toolCallId]) {
        acc[toolCallId] = []
      }
      acc[toolCallId].push(item)
      return acc
    },
    {} as Record<string, ToolInvocationUIPart[]>
  )

  const uniqueToolIds = Object.keys(groupedTools)
  
  // Check if all tools are completed
  const allToolsCompleted = useMemo(() => {
    return toolInvocationsData.every(
      (tool) => tool.toolInvocation.state === "result"
    )
  }, [toolInvocationsData])

  // Check if any tool is loading
  const anyToolLoading = useMemo(() => {
    return toolInvocationsData.some(
      (tool) => tool.toolInvocation.state === "call" || tool.toolInvocation.state === "partial-call"
    )
  }, [toolInvocationsData])

  // Auto-hide after completion
  useEffect(() => {
    if (allToolsCompleted && autoHideDelay > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false)
      }, autoHideDelay)

      return () => clearTimeout(timer)
    }
  }, [allToolsCompleted, autoHideDelay])

  // Don't render if not visible
  if (!isVisible) {
    return null
  }

  // Show shimmer loader while any tool is loading
  if (anyToolLoading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={TRANSITION}
        className={cn("flex items-center gap-3 p-4 rounded-lg border border-border bg-background/50", className)}
      >
        <Wrench className="text-muted-foreground size-4" />
        <Loader 
          variant="text-shimmer" 
          text="Running tools..." 
          className="text-sm text-muted-foreground"
        />
      </motion.div>
    )
  }

  // Show completion state briefly before auto-hide
  if (allToolsCompleted) {
    return (
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95, height: 0 }}
          transition={TRANSITION}
          className={cn("flex items-center gap-3 p-4 rounded-lg border border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/20", className)}
        >
          <CheckCircle className="size-4 text-green-600 dark:text-green-400" />
          <span className="text-sm text-green-700 dark:text-green-300">
            {uniqueToolIds.length} tool{uniqueToolIds.length > 1 ? 's' : ''} completed successfully
          </span>
        </motion.div>
      </AnimatePresence>
    )
  }

  return null
}

// Optional: Enhanced version with detailed tool info (if you want to show more details)
export function DetailedToolInvocation({
  toolInvocations,
  className,
  autoHideDelay = 3000,
  showDetails = false,
}: ToolInvocationProps & { showDetails?: boolean }) {
  const [isVisible, setIsVisible] = useState(true)
  const [showDetailedView, setShowDetailedView] = useState(showDetails)
  
  const toolInvocationsData = Array.isArray(toolInvocations)
    ? toolInvocations
    : [toolInvocations]

  const groupedTools = toolInvocationsData.reduce(
    (acc, item) => {
      const { toolCallId } = item.toolInvocation
      if (!acc[toolCallId]) {
        acc[toolCallId] = []
      }
      acc[toolCallId].push(item)
      return acc
    },
    {} as Record<string, ToolInvocationUIPart[]>
  )

  const uniqueToolIds = Object.keys(groupedTools)
  
  const allToolsCompleted = useMemo(() => {
    return toolInvocationsData.every(
      (tool) => tool.toolInvocation.state === "result"
    )
  }, [toolInvocationsData])

  const anyToolLoading = useMemo(() => {
    return toolInvocationsData.some(
      (tool) => tool.toolInvocation.state === "call" || tool.toolInvocation.state === "partial-call"
    )
  }, [toolInvocationsData])

  const currentlyRunningTools = useMemo(() => {
    return toolInvocationsData
      .filter(tool => tool.toolInvocation.state === "call" || tool.toolInvocation.state === "partial-call")
      .map(tool => tool.toolInvocation.toolName)
  }, [toolInvocationsData])

  useEffect(() => {
    if (allToolsCompleted && autoHideDelay > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false)
      }, autoHideDelay)

      return () => clearTimeout(timer)
    }
  }, [allToolsCompleted, autoHideDelay])

  if (!isVisible) {
    return null
  }

  if (anyToolLoading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={TRANSITION}
        className={cn("rounded-lg border border-border bg-background/50", className)}
      >
        <div className="p-4">
          <div className="flex items-center gap-3 mb-3">
            <Wrench className="text-muted-foreground size-4" />
            <Loader 
              variant="text-shimmer" 
              text="Processing..." 
              className="text-sm text-muted-foreground"
            />
          </div>
          
          {currentlyRunningTools.length > 0 && (
            <div className="space-y-2">
              {currentlyRunningTools.map((toolName, index) => (
                <div key={index} className="flex items-center gap-2 text-xs text-muted-foreground">
                  <div className="size-1.5 rounded-full bg-blue-500 animate-pulse" />
                  <span className="font-mono">{toolName}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      </motion.div>
    )
  }

  if (allToolsCompleted) {
    return (
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95, height: 0 }}
          transition={TRANSITION}
          className={cn("rounded-lg border border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/20", className)}
        >
          <div className="p-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="size-4 text-green-600 dark:text-green-400" />
              <span className="text-sm text-green-700 dark:text-green-300">
                Completed {uniqueToolIds.length} tool{uniqueToolIds.length > 1 ? 's' : ''}
              </span>
              
              {showDetailedView && (
                <button
                  onClick={() => setShowDetailedView(false)}
                  className="text-xs text-green-600 dark:text-green-400 hover:underline ml-auto"
                >
                  Hide details
                </button>
              )}
            </div>
            
            {showDetailedView && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                className="mt-3 space-y-1"
              >
                {Object.entries(groupedTools).map(([toolId, tools]) => {
                  const tool = tools.find(t => t.toolInvocation.state === "result") || tools[0]
                  return (
                    <div key={toolId} className="flex items-center gap-2 text-xs text-green-600 dark:text-green-400">
                      <CheckCircle className="size-3" />
                      <span className="font-mono">{tool.toolInvocation.toolName}</span>
                    </div>
                  )
                })}
              </motion.div>
            )}
          </div>
        </motion.div>
      </AnimatePresence>
    )
  }

  return null
}
