"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { useUser } from "@/lib/user-store/provider"
import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { AppInfoTrigger } from "./app-info/app-info-trigger"
import { FeedbackTrigger } from "./feedback/feedback-trigger"
import { SettingsTrigger } from "./settings/settings-trigger"

// Bounce animation variants
const bounceVariants = {
  initial: { 
    scale: 0.3, 
    opacity: 0,
    y: -20
  },
  animate: { 
    scale: 1, 
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 260,
      damping: 20,
      bounce: 0.4
    }
  },
  exit: { 
    scale: 0.3, 
    opacity: 0,
    y: -20,
    transition: {
      duration: 0.2
    }
  }
}

const avatarHoverVariants = {
  hover: { 
    scale: 1.1,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 17,
      bounce: 0.6
    }
  }
}

const menuItemVariants = {
  initial: { x: -20, opacity: 0 },
  animate: (i: number) => ({
    x: 0,
    opacity: 1,
    transition: {
      delay: i * 0.1,
      type: "spring",
      stiffness: 200,
      damping: 20,
      bounce: 0.3
    }
  })
}

export function UserMenu() {
  const { user } = useUser()
  const [isMenuOpen, setMenuOpen] = useState(false)
  const [isSettingsOpen, setSettingsOpen] = useState(false)

  if (!user) return null

  const handleSettingsOpenChange = (isOpen: boolean) => {
    setSettingsOpen(isOpen)
    if (!isOpen) {
      setMenuOpen(false)
    }
  }

  return (
    // fix shadcn/ui / radix bug when dialog into dropdown menu
    <DropdownMenu open={isMenuOpen} onOpenChange={setMenuOpen} modal={false}>
      <Tooltip>
        <TooltipTrigger asChild>
          <DropdownMenuTrigger asChild>
            <motion.div
              variants={avatarHoverVariants}
              whileHover="hover"
              whileTap={{ scale: 0.95 }}
            >
              <Avatar className="bg-background hover:bg-muted cursor-pointer">
                <AvatarImage src={user?.profile_image ?? undefined} />
                <AvatarFallback>{user?.display_name?.charAt(0)}</AvatarFallback>
              </Avatar>
            </motion.div>
          </DropdownMenuTrigger>
        </TooltipTrigger>
        <TooltipContent>Profile</TooltipContent>
      </Tooltip>

      <AnimatePresence>
        {isMenuOpen && (
          <DropdownMenuContent
            className="w-56"
            align="end"
            forceMount
            onCloseAutoFocus={(e) => e.preventDefault()}
            onInteractOutside={(e) => {
              if (isSettingsOpen) {
                e.preventDefault()
                return
              }
              setMenuOpen(false)
            }}
            asChild
          >
            <motion.div
              variants={bounceVariants}
              initial="initial"
              animate="animate"
              exit="exit"
            >
              <motion.div
                variants={menuItemVariants}
                initial="initial"
                animate="animate"
                custom={0}
              >
                <DropdownMenuItem className="flex flex-col items-start gap-0 no-underline hover:bg-transparent focus:bg-transparent">
                  <span>{user?.display_name}</span>
                  <span className="text-muted-foreground max-w-full truncate">
                    {user?.email}
                  </span>
                </DropdownMenuItem>
              </motion.div>
              
              <DropdownMenuSeparator />
              
              <motion.div
                variants={menuItemVariants}
                initial="initial"
                animate="animate"
                custom={1}
              >
                <SettingsTrigger onOpenChange={handleSettingsOpenChange} />
              </motion.div>
              
              <motion.div
                variants={menuItemVariants}
                initial="initial"
                animate="animate"
                custom={2}
              >
                <FeedbackTrigger />
              </motion.div>
              
              <motion.div
                variants={menuItemVariants}
                initial="initial"
                animate="animate"
                custom={3}
              >
                <AppInfoTrigger />
              </motion.div>
            </motion.div>
          </DropdownMenuContent>
        )}
      </AnimatePresence>
    </DropdownMenu>
  )
}
