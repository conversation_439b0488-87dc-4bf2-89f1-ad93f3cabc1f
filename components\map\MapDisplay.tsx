'use client';

import React, { useEffect, useRef, useState } from 'react';

// Define interfaces
interface MapData {
  type: 'hotel' | 'coaching';
  coordinates: [number, number]; // [longitude, latitude] - ये order database से आता है
  popupContent: {
    name: string;
    rating?: number;
    contact?: string;
    [key: string]: any;
  };
}

interface MapDisplayProps {
  results: Array<{
    mapData: MapData;
    [key: string]: any;
  }>;
  mapCenter?: [number, number];
  height?: string;
  zoom?: number;
}

const MapDisplay: React.FC<MapDisplayProps> = ({ 
  results, 
  mapCenter, 
  height = '400px',
  zoom = 12 
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient || !mapRef.current || !results.length) return;

    let L: any;
    let isMounted = true;

    const initializeMap = async () => {
      try {
        L = (await import('leaflet')).default;
        await import('leaflet/dist/leaflet.css');

        // Fix for default markers
        delete (L.Icon.Default.prototype as any)._getIconUrl;
        L.Icon.Default.mergeOptions({
          iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
          iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
          shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
        });

        if (!isMounted || !mapRef.current) return;

        // Clean up existing map
        if (mapInstanceRef.current) {
          mapInstanceRef.current.remove();
          mapInstanceRef.current = null;
        }

        // Initialize map - Leaflet uses [lat, lng] format
        const initialCenter = mapCenter || [results[0].mapData.coordinates[1], results[0].mapData.coordinates[0]];
        const map = L.map(mapRef.current).setView(initialCenter, zoom);

        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '© OpenStreetMap contributors',
          maxZoom: 19,
        }).addTo(map);

        // Custom icons
        const hotelIcon = L.divIcon({
          className: 'custom-marker hotel-marker',
          html: '<div style="background: #ef4444; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold;">🏨</div>',
          iconSize: [30, 30],
          iconAnchor: [15, 30],
        });

        const coachingIcon = L.divIcon({
          className: 'custom-marker coaching-marker', 
          html: '<div style="background: #3b82f6; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold;">🎓</div>',
          iconSize: [30, 30],
          iconAnchor: [15, 30],
        });

        const markers: any[] = [];

        // Add markers for each result
        results.forEach((result) => {
          const { mapData } = result;
          const icon = mapData.type === 'hotel' ? hotelIcon : coachingIcon;
          
          // Leaflet uses [lat, lng], database stores [lng, lat]
          const lat = mapData.coordinates[1];
          const lng = mapData.coordinates[0];
          
          const marker = L.marker([lat, lng], { icon }).addTo(map);
          markers.push(marker);

          // Fixed popup content with proper coordinate interpolation
          const popupContent = `
            <div style="min-width: 200px;">
              <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: bold;">
                ${mapData.popupContent.name}
              </h3>
              ${mapData.popupContent.rating ? `
                <p style="margin: 4px 0;">
                  <strong>Rating:</strong> ${mapData.popupContent.rating}/5 ⭐
                </p>
              ` : ''}
              ${mapData.popupContent.contact ? `
                <p style="margin: 4px 0;">
                  <strong>Contact:</strong> ${mapData.popupContent.contact}
                </p>
              ` : ''}
              ${mapData.type === 'hotel' && mapData.popupContent.pricing ? `
                <p style="margin: 4px 0;">
                  <strong>Pricing:</strong> ${mapData.popupContent.pricing}
                </p>
              ` : ''}
              ${mapData.type === 'coaching' && mapData.popupContent.courses ? `
                <p style="margin: 4px 0;">
                  <strong>Courses:</strong> ${mapData.popupContent.courses}
                </p>
              ` : ''}
              <button 
                onclick="navigator.geolocation.getCurrentPosition(pos => {
                  const userLat = pos.coords.latitude;
                  const userLng = pos.coords.longitude;
                  window.open('https://www.openstreetmap.org/directions?from=' + userLat + ',' + userLng + '&to=' + ${lat} + ',' + ${lng}, '_blank');
                }, err => {
                  window.open('https://www.openstreetmap.org/directions?to=' + ${lat} + ',' + ${lng}, '_blank');
                })"
                style="margin-top: 8px; padding: 4px 8px; background: #10b981; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;"
              >
                Get Directions 🗺️
              </button>
            </div>
          `;

          marker.bindPopup(popupContent);
        });

        // Fit map to show all markers
        if (markers.length > 1) {
          const group = new L.featureGroup(markers);
          map.fitBounds(group.getBounds().pad(0.1));
        } else if (markers.length === 1) {
          // Center on single marker
          map.setView([results[0].mapData.coordinates[1], results[0].mapData.coordinates[0]], zoom);
        }

        mapInstanceRef.current = map;
      } catch (error) {
        console.error('Error initializing map:', error);
      }
    };

    initializeMap();

    return () => {
      isMounted = false;
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [results, mapCenter, zoom, isClient]);

  if (!isClient) {
    return (
      <div 
        className="flex items-center justify-center border rounded-lg bg-muted/30"
        style={{ height }}
      >
        <p className="text-muted-foreground">Loading map...</p>
      </div>
    );
  }

  if (!results.length) {
    return (
      <div 
        className="flex items-center justify-center border rounded-lg bg-muted/30"
        style={{ height }}
      >
        <p className="text-muted-foreground">No locations to display on map</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">📍 Locations Map</h3>
        <div className="flex gap-4 text-sm">
          <div className="flex items-center gap-1">
            <span>🏨</span>
            <span>Hotels ({results.filter(r => r.mapData.type === 'hotel').length})</span>
          </div>
          <div className="flex items-center gap-1">
            <span>🎓</span>
            <span>Coaching ({results.filter(r => r.mapData.type === 'coaching').length})</span>
          </div>
        </div>
      </div>
      
      <div 
        ref={mapRef} 
        className="w-full border rounded-lg"
        style={{ height }}
      />
      
      <p className="text-xs text-muted-foreground text-center">
        Click markers for details • Powered by OpenStreetMap
      </p>
    </div>
  );
};

export default MapDisplay;
