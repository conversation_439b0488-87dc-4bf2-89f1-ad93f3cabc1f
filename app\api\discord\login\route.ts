import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const userId = searchParams.get('userId')

  if (!userId) {
    return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
  }

  // Validate environment variables
  if (!process.env.DISCORD_CLIENT_ID || !process.env.DISCORD_REDIRECT_URI) {
    console.error('Missing Discord environment variables')
    return NextResponse.json({ 
      error: 'Discord configuration missing' 
    }, { status: 500 })
  }

  // ✅ Only use valid Discord OAuth scopes (public scopes that don't require approval)
  const scopes = [
    'identify',    // Basic user profile info
    'email',       // User's email address
    'guilds',      // User's Discord servers
    'connections'  // User's connected accounts (Spotify, Steam, etc.)
  ].join(' ')

  console.log('Initiating Discord OAuth for user:', userId.substring(0, 8) + '...')
  console.log('Using scopes:', scopes)

  try {
    const discordAuthUrl = new URL('https://discord.com/api/oauth2/authorize')
    discordAuthUrl.searchParams.append('client_id', process.env.DISCORD_CLIENT_ID)
    discordAuthUrl.searchParams.append('redirect_uri', process.env.DISCORD_REDIRECT_URI)
    discordAuthUrl.searchParams.append('response_type', 'code')
    discordAuthUrl.searchParams.append('scope', scopes)
    discordAuthUrl.searchParams.append('state', userId) // Pass userId as state for security
    discordAuthUrl.searchParams.append('prompt', 'consent') // Always show consent screen

    console.log('Redirecting to Discord OAuth URL:', discordAuthUrl.toString())

    return NextResponse.redirect(discordAuthUrl.toString())
  } catch (error) {
    console.error('Error building Discord OAuth URL:', error)
    return NextResponse.json({ 
      error: 'Failed to initiate Discord OAuth' 
    }, { status: 500 })
  }
}
