"use client"

import { <PERSON><PERSON>, Ava<PERSON><PERSON><PERSON>back, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useUser } from "@/lib/user-store/provider"
import { User, MusicNote, LinkBreak, Check, BookOpen, Note, SpotifyLogo, NotionLogo, DiscordLogo, GameController, Phone, Shield } from "@phosphor-icons/react"
import { useEffect, useState } from "react"

interface SpotifyStatus {
  linked: boolean
  currentlyPlaying?: string | null
  loginUrl?: string
  isPlaying?: boolean
  error?: string
}

interface NotionStatus {
  linked: boolean
  workspaceName?: string
  loginUrl?: string
  error?: string
  lastSync?: string
}

interface DiscordStatus {
  linked: boolean
  currentActivity?: string | null
  activityType?: string
  loginUrl?: string
  username?: string
  error?: string
  isOnline?: boolean
}

interface PhoneStatus {
  linked: boolean
  phoneNumber?: string
  error?: string
}

export function UserProfile() {
  const { user } = useUser()
  const [spotifyStatus, setSpotifyStatus] = useState<SpotifyStatus>({ linked: false })
  const [notionStatus, setNotionStatus] = useState<NotionStatus>({ linked: false })
  const [discordStatus, setDiscordStatus] = useState<DiscordStatus>({ linked: false })
  const [phoneStatus, setPhoneStatus] = useState<PhoneStatus>({ linked: false })
  const [phoneDialog, setPhoneDialog] = useState(false)
  const [phoneNumber, setPhoneNumber] = useState("")
  const [otp, setOtp] = useState("")
  const [step, setStep] = useState<"phone" | "otp">("phone")
  const [loading, setLoading] = useState({ 
    spotify: false, 
    notion: false,
    discord: false,
    phone: false,
    initialLoad: true 
  })

  // Fetch Spotify status
  useEffect(() => {
    if (!user?.id) return

    const fetchSpotifyStatus = async () => {
      try {
        const response = await fetch(`/api/spotify/status?userId=${user.id}`)
        if (response.ok) {
          const data = await response.json()
          setSpotifyStatus(data)
        }
      } catch (error) {
        console.error('Failed to fetch Spotify status:', error)
      } finally {
        setLoading(prev => ({ ...prev, initialLoad: false }))
      }
    }

    fetchSpotifyStatus()
    const interval = spotifyStatus.linked ? setInterval(fetchSpotifyStatus, 30000) : null
    return () => interval && clearInterval(interval)
  }, [user?.id, spotifyStatus.linked])

  // Fetch Notion status
  useEffect(() => {
    if (!user?.id) return

    const fetchNotionStatus = async () => {
      try {
        const response = await fetch(`/api/notion/status?userId=${user.id}`)
        if (response.ok) {
          const data = await response.json()
          setNotionStatus(data)
        }
      } catch (error) {
        console.error('Failed to fetch Notion status:', error)
      }
    }

    fetchNotionStatus()
  }, [user?.id])

  // Fetch Discord status
  useEffect(() => {
    if (!user?.id) return

    const fetchDiscordStatus = async () => {
      try {
        const response = await fetch(`/api/discord/status?userId=${user.id}`)
        if (response.ok) {
          const data = await response.json()
          setDiscordStatus(data)
        }
      } catch (error) {
        console.error('Failed to fetch Discord status:', error)
      }
    }

    fetchDiscordStatus()
    const interval = discordStatus.linked ? setInterval(fetchDiscordStatus, 60000) : null
    return () => interval && clearInterval(interval)
  }, [user?.id, discordStatus.linked])

  // Fetch phone status
  useEffect(() => {
    if (!user?.id) return

    const fetchPhoneStatus = async () => {
      try {
        const response = await fetch(`/api/phone/status?userId=${user.id}`)
        if (response.ok) {
          const data = await response.json()
          setPhoneStatus(data)
        }
      } catch (error) {
        console.error('Failed to fetch phone status:', error)
      }
    }

    fetchPhoneStatus()
  }, [user?.id])

  // Listen for auth messages
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'NOTION_AUTH_SUCCESS') {
        if (user?.id) {
          fetch(`/api/notion/status?userId=${user.id}`)
            .then(res => res.json())
            .then(data => setNotionStatus(data))
        }
      } else if (event.data.type === 'NOTION_AUTH_ERROR') {
        console.error('Notion auth error:', event.data.error)
        setNotionStatus(prev => ({ ...prev, error: event.data.error }))
      } else if (event.data.type === 'DISCORD_AUTH_SUCCESS') {
        if (user?.id) {
          fetch(`/api/discord/status?userId=${user.id}`)
            .then(res => res.json())
            .then(data => setDiscordStatus(data))
        }
      } else if (event.data.type === 'DISCORD_AUTH_ERROR') {
        console.error('Discord auth error:', event.data.error)
        setDiscordStatus(prev => ({ ...prev, error: event.data.error }))
      }
    }

    window.addEventListener('message', handleMessage)
    return () => window.removeEventListener('message', handleMessage)
  }, [user?.id])

  const handleSpotifyLink = () => {
    if (!user?.id) return

    const loginUrl = spotifyStatus.loginUrl || `/api/spotify/login?userId=${user.id}`
    window.open(loginUrl, '_blank', 'width=500,height=600')

    setTimeout(() => {
      if (user?.id) {
        fetch(`/api/spotify/status?userId=${user.id}`)
          .then(res => res.json())
          .then(data => setSpotifyStatus(data))
      }
    }, 3000)
  }

  const handleSpotifyUnlink = async () => {
    if (!user?.id) return

    setLoading(prev => ({ ...prev, spotify: true }))
    try {
      const response = await fetch(`/api/spotify/unlink?userId=${user.id}`, {
        method: 'POST'
      })
      if (response.ok) {
        setSpotifyStatus({ linked: false })
      }
    } catch (error) {
      console.error('Failed to unlink Spotify:', error)
    } finally {
      setLoading(prev => ({ ...prev, spotify: false }))
    }
  }

  const handleNotionLink = () => {
    if (!user?.id) return

    const loginUrl = notionStatus.loginUrl || `/api/notion/login?userId=${user.id}`
    window.open(loginUrl, '_blank', 'width=500,height=700')
  }

  const handleNotionUnlink = async () => {
    if (!user?.id) return

    setLoading(prev => ({ ...prev, notion: true }))
    try {
      const response = await fetch(`/api/notion/unlink?userId=${user.id}`, {
        method: 'POST'
      })
      if (response.ok) {
        setNotionStatus({ linked: false })
      }
    } catch (error) {
      console.error('Failed to unlink Notion:', error)
    } finally {
      setLoading(prev => ({ ...prev, notion: false }))
    }
  }

  const handleDiscordLink = () => {
    if (!user?.id) return

    const loginUrl = discordStatus.loginUrl || `/api/discord/login?userId=${user.id}`
    window.open(loginUrl, '_blank', 'width=500,height=600')
  }

  const handleDiscordUnlink = async () => {
    if (!user?.id) return

    setLoading(prev => ({ ...prev, discord: true }))
    try {
      const response = await fetch(`/api/discord/unlink?userId=${user.id}`, {
        method: 'POST'
      })
      if (response.ok) {
        setDiscordStatus({ linked: false })
      }
    } catch (error) {
      console.error('Failed to unlink Discord:', error)
    } finally {
      setLoading(prev => ({ ...prev, discord: false }))
    }
  }

  const handleSendOTP = async () => {
    if (!phoneNumber.trim() || !user?.id) return

    setLoading(prev => ({ ...prev, phone: true }))
    setPhoneStatus(prev => ({ ...prev, error: undefined }))
    
    try {
      const response = await fetch('/api/phone/send-otp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ phoneNumber, userId: user.id })
      })
      
      const data = await response.json()
      if (response.ok) {
        setStep("otp")
      } else {
        setPhoneStatus(prev => ({ ...prev, error: data.error || "Failed to send OTP" }))
      }
    } catch (error) {
      setPhoneStatus(prev => ({ ...prev, error: "Network error. Please try again." }))
    } finally {
      setLoading(prev => ({ ...prev, phone: false }))
    }
  }

  const handleVerifyOTP = async () => {
    if (!otp.trim() || !phoneNumber || !user?.id) return

    setLoading(prev => ({ ...prev, phone: true }))
    setPhoneStatus(prev => ({ ...prev, error: undefined }))
    
    try {
      const response = await fetch('/api/phone/verify-otp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ phoneNumber, otp, userId: user.id })
      })
      
      const data = await response.json()
      if (response.ok) {
        setPhoneStatus({ linked: true, phoneNumber })
        setPhoneDialog(false)
        setStep("phone")
        setPhoneNumber("")
        setOtp("")
      } else {
        setPhoneStatus(prev => ({ ...prev, error: data.error || "Invalid OTP" }))
      }
    } catch (error) {
      setPhoneStatus(prev => ({ ...prev, error: "Network error. Please try again." }))
    } finally {
      setLoading(prev => ({ ...prev, phone: false }))
    }
  }

  const handlePhoneUnlink = async () => {
    if (!user?.id) return

    setLoading(prev => ({ ...prev, phone: true }))
    try {
      const response = await fetch(`/api/phone/unlink?userId=${user.id}`, {
        method: 'POST'
      })
      if (response.ok) {
        setPhoneStatus({ linked: false })
      }
    } catch (error) {
      console.error('Failed to unlink phone:', error)
    } finally {
      setLoading(prev => ({ ...prev, phone: false }))
    }
  }

  const resetPhoneDialog = () => {
    setStep("phone")
    setPhoneNumber("")
    setOtp("")
    setPhoneStatus(prev => ({ ...prev, error: undefined }))
  }

  // Show skeleton loader when no user or during initial load
  if (!user || loading.initialLoad) {
    return (
      <div className="space-y-6">
        {/* Profile Skeleton */}
        <div>
          <Skeleton className="h-4 w-16 mb-3" />
          <div className="flex items-center space-x-4">
            <Skeleton className="size-12 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-48" />
            </div>
          </div>
        </div>

        {/* Integrations Skeleton */}
        <div className="space-y-4">
          <Skeleton className="h-4 w-24" />
          
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="flex items-center justify-between">
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-28" />
                <Skeleton className="h-3 w-56" />
              </div>
              <Skeleton className="h-8 w-20" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* User Profile Section */}
      <div>
        <h3 className="mb-3 text-sm font-medium">Profile</h3>
        <div className="flex items-center space-x-4">
          <div className="bg-muted flex items-center justify-center overflow-hidden rounded-full">
            {user?.profile_image ? (
              <Avatar className="size-12">
                <AvatarImage src={user.profile_image} className="object-cover" />
                <AvatarFallback>{user?.display_name?.charAt(0)}</AvatarFallback>
              </Avatar>
            ) : (
              <User className="text-muted-foreground size-12" />
            )}
          </div>
          <div>
            <h4 className="text-sm font-medium">{user?.display_name}</h4>
            <p className="text-muted-foreground text-sm">{user?.email}</p>
          </div>
        </div>
      </div>

      {/* Integrations Section */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Connected Apps</h3>

        {/* Phone Number Integration */}
        <div className="space-y-3">
          {phoneStatus.linked ? (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Phone className="w-7 h-7 text-green-500" />
                <div className="flex flex-col">
                  <span className="text-sm text-green-600 font-medium">Phone Connected</span>
                  <span className="text-xs text-muted-foreground">
                    {phoneStatus.phoneNumber}
                  </span>
                </div>
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={handlePhoneUnlink}
                disabled={loading.phone}
                className="text-xs"
              >
                {loading.phone ? (
                  <div className="flex items-center gap-1">
                    <Skeleton className="w-3 h-3" />
                    <span>Disconnecting...</span>
                  </div>
                ) : (
                  <>
                    <LinkBreak className="w-3 h-3 mr-1" />
                    Disconnect
                  </>
                )}
              </Button>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between">
                <div className="gap-2">
                  <span className="text-sm text-black font-medium">Phone Connect</span>
                  <p className="text-xs text-muted-foreground">
                    Connect your phone number for SMS notifications and verification
                  </p>
                </div>

                <Dialog 
                  open={phoneDialog} 
                  onOpenChange={(open) => {
                    setPhoneDialog(open)
                    if (!open) resetPhoneDialog()
                  }}
                >
                  <DialogTrigger asChild>
                    <Button size="sm" className="text-xs bg-black hover:bg-gray-800">
                      <Phone className="w-3 h-3 mr-1" />
                      Connect
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle className="flex items-center gap-2">
                        <Phone className="w-5 h-5" />
                        {step === "phone" ? "Connect Phone Number" : "Verify OTP"}
                      </DialogTitle>
                    </DialogHeader>
                    
                    {step === "phone" ? (
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="phone">Phone Number</Label>
                          <Input
                            id="phone"
                            type="tel"
                            placeholder="+91 9876543210"
                            value={phoneNumber}
                            onChange={(e) => setPhoneNumber(e.target.value)}
                            className="text-sm"
                          />
                          <p className="text-xs text-muted-foreground">
                            We'll send an OTP via push notification to verify your number
                          </p>
                        </div>
                        
                        {phoneStatus.error && (
                          <div className="text-xs text-red-600 bg-red-50 border border-red-200 p-2 rounded">
                            {phoneStatus.error}
                          </div>
                        )}

                        <div className="flex gap-2 pt-2">
                          <Button
                            onClick={() => setPhoneDialog(false)}
                            variant="outline"
                            className="flex-1"
                            disabled={loading.phone}
                          >
                            Cancel
                          </Button>
                          <Button
                            onClick={handleSendOTP}
                            disabled={!phoneNumber.trim() || loading.phone}
                            className="flex-1"
                          >
                            {loading.phone ? (
                              <>
                                <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                Sending...
                              </>
                            ) : (
                              "Send OTP"
                            )}
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="otp">Enter OTP</Label>
                          <Input
                            id="otp"
                            type="text"
                            placeholder="123456"
                            value={otp}
                            onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
                            maxLength={6}
                            className="text-center text-lg tracking-widest"
                          />
                          <p className="text-xs text-muted-foreground">
                            OTP sent to {phoneNumber}
                          </p>
                        </div>

                        {phoneStatus.error && (
                          <div className="text-xs text-red-600 bg-red-50 border border-red-200 p-2 rounded">
                            {phoneStatus.error}
                          </div>
                        )}

                        <div className="flex gap-2 pt-2">
                          <Button
                            onClick={() => {
                              setStep("phone")
                              setOtp("")
                              setPhoneStatus(prev => ({ ...prev, error: undefined }))
                            }}
                            variant="outline"
                            className="flex-1"
                            disabled={loading.phone}
                          >
                            Back
                          </Button>
                          <Button
                            onClick={handleVerifyOTP}
                            disabled={otp.length !== 6 || loading.phone}
                            className="flex-1"
                          >
                            {loading.phone ? (
                              <>
                                <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                Verifying...
                              </>
                            ) : (
                              <>
                                <Shield className="w-3 h-3 mr-1" />
                                Verify
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    )}
                  </DialogContent>
                </Dialog>
              </div>
            </>
          )}
        </div>

        {/* Notion Integration */}
        <div className="space-y-3">
          {notionStatus.linked ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <NotionLogo className="w-7 h-7 text-green-500" />
                  <span className="text-sm text-green-600 font-medium">Notion Connected</span>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleNotionUnlink}
                  disabled={loading.notion}
                  className="text-xs"
                >
                  {loading.notion ? (
                    <div className="flex items-center gap-1">
                      <Skeleton className="w-3 h-3" />
                      <span>Disconnecting...</span>
                    </div>
                  ) : (
                    <>
                      <LinkBreak className="w-3 h-3 mr-1" />
                      Disconnect
                    </>
                  )}
                </Button>
              </div>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between">
                <div className="gap-2">
                  <span className="text-sm text-black font-medium">Notion Connect</span>
                  <p className="text-xs text-muted-foreground">
                    Connect your Notion workspace to create and manage pages through chat
                  </p>
                </div>

                <Button
                  size="sm"
                  onClick={handleNotionLink}
                  className="text-xs bg-black"
                >
                  <Note className="w-3 h-3 mr-1" />
                  Connect
                </Button>
              </div>
              {notionStatus.error && (
                <div className="text-xs text-red-500 bg-red-50 p-2 rounded">
                  {notionStatus.error}
                </div>
              )}
            </>
          )}
        </div>

        {/* Discord Integration */}
        <div className="space-y-3">
          {discordStatus.linked ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <DiscordLogo className="w-7 h-7 text-green-500" />
                  <div className="flex flex-col">
                    <span className="text-sm text-green-600 font-medium">Discord Connected</span>
                    {discordStatus.currentActivity && (
                      <span className="text-xs text-muted-foreground">
                        {discordStatus.activityType}: {discordStatus.currentActivity}
                      </span>
                    )}
                    {discordStatus.username && (
                      <span className="text-xs text-muted-foreground">
                        @{discordStatus.username}
                      </span>
                    )}
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleDiscordUnlink}
                  disabled={loading.discord}
                  className="text-xs"
                >
                  {loading.discord ? (
                    <div className="flex items-center gap-1">
                      <Skeleton className="w-3 h-3" />
                      <span>Disconnecting...</span>
                    </div>
                  ) : (
                    <>
                      <LinkBreak className="w-3 h-3 mr-1" />
                      Disconnect
                    </>
                  )}
                </Button>
              </div>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between">
                <div className="gap-2">
                  <span className="text-sm text-black font-medium">Discord Connect</span>
                  <p className="text-xs text-muted-foreground">
                    Connect your Discord account to check activity and status through chat
                  </p>
                </div>

                <Button
                  size="sm"
                  onClick={handleDiscordLink}
                  className="text-xs bg-black"
                >
                  <GameController className="w-3 h-3 mr-1" />
                  Connect
                </Button>
              </div>
              {discordStatus.error && (
                <div className="text-xs text-red-500 bg-red-50 p-2 rounded">
                  {discordStatus.error}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
}
