import { useEffect, useState, useCallback, useRef } from 'react';

type SpotifyStatus = {
  linked: boolean;
  currentlyPlaying: string | null;
  isPlaying?: boolean;
  track?: {
    name: string;
    artist: string;
    album?: string;
    duration_ms?: number;
    progress_ms?: number;
    albumArtwork?: {
      large: string;
      medium: string;
      small: string;
      default: string;
    };
    albumImage?: string;
  };
};

export const useSpotifyRealTime = (
  userId?: string, 
  shouldFetch: boolean = false,
  useSSE: boolean = true // Toggle between SSE and polling
) => {
  const [status, setStatus] = useState<SpotifyStatus>({
    linked: false,
    currentlyPlaying: null,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [connected, setConnected] = useState(false);
  
  const intervalRef = useRef<NodeJS.Timeout>();
  const progressIntervalRef = useRef<NodeJS.Timeout>();
  const eventSourceRef = useRef<EventSource>();

  // SSE Implementation
  const connectSSE = useCallback(() => {
    if (!userId || !shouldFetch) return;

    setLoading(true);
    setError(null);

    const eventSource = new EventSource(`/api/spotify/realtimestatus?userId=${userId}&stream=true`);
    eventSourceRef.current = eventSource;

    eventSource.onopen = () => {
      setConnected(true);
      setLoading(false);
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'status') {
          setStatus(data.data);
        } else if (data.type === 'error') {
          setError(data.error);
        }
      } catch (err) {
        console.error('Error parsing SSE data:', err);
      }
    };

    eventSource.onerror = (event) => {
      console.error('SSE connection error:', event);
      setConnected(false);
      setError('Connection lost. Reconnecting...');
      
      // Reconnect after 5 seconds
      setTimeout(() => {
        if (eventSourceRef.current?.readyState === EventSource.CLOSED) {
          connectSSE();
        }
      }, 5000);
    };

    return eventSource;
  }, [userId, shouldFetch]);

  // Polling Implementation
  const fetchStatus = useCallback(async () => {
    if (!userId || !shouldFetch) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/spotify/realtimestatus?userId=${userId}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      setStatus(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('Error fetching Spotify status:', err);
    } finally {
      setLoading(false);
    }
  }, [userId, shouldFetch]);

  // Progress bar real-time updates
  useEffect(() => {
    if (status.isPlaying && status.track?.progress_ms !== undefined) {
      progressIntervalRef.current = setInterval(() => {
        setStatus(prevStatus => ({
          ...prevStatus,
          track: prevStatus.track ? {
            ...prevStatus.track,
            progress_ms: Math.min(
              (prevStatus.track.progress_ms || 0) + 1000,
              prevStatus.track.duration_ms || 0
            )
          } : undefined
        }));
      }, 1000);

      return () => {
        if (progressIntervalRef.current) {
          clearInterval(progressIntervalRef.current);
        }
      };
    } else {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    }
  }, [status.isPlaying, status.track?.progress_ms]);

  // Main connection logic
  useEffect(() => {
    if (shouldFetch && userId) {
      if (useSSE) {
        // Use Server-Sent Events
        connectSSE();
        
        return () => {
          if (eventSourceRef.current) {
            eventSourceRef.current.close();
          }
          if (progressIntervalRef.current) {
            clearInterval(progressIntervalRef.current);
          }
        };
      } else {
        // Use polling
        fetchStatus();
        intervalRef.current = setInterval(fetchStatus, 10000); // Poll every 10 seconds

        return () => {
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
          }
          if (progressIntervalRef.current) {
            clearInterval(progressIntervalRef.current);
          }
        };
      }
    }
  }, [shouldFetch, userId, useSSE, connectSSE, fetchStatus]);

  const refetch = useCallback(() => {
    if (useSSE) {
      // Reconnect SSE
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
      connectSSE();
    } else {
      // Manual fetch for polling
      fetchStatus();
    }
  }, [useSSE, connectSSE, fetchStatus]);

  return {
    status,
    loading,
    error,
    connected: useSSE ? connected : true,
    refetch
  };
};
