"use client"

import { Switch } from "@/components/ui/switch"
import { useUserPreferences } from "@/lib/user-preference-store/provider"
import { useState, useEffect, useCallback } from "react"
import { createBrowserClient } from "@supabase/ssr"
import { useUser } from "@/lib/user-store/provider"

// Supabase client
const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export function InteractionPreferences() {
  const {
    preferences,
    setPromptSuggestions,
    setShowToolInvocations,
    setShowConversationPreviews,
  } = useUserPreferences()

  const { user } = useUser()
  const userId = user?.id
  const [shareData, setShareData] = useState(false)

  // Fetch shareData from Supabase
  const fetchShareData = useCallback(async () => {
    if (!userId) return

    const { data, error } = await supabase
      .from("user_data_control")
      .select("share_data")
      .eq("user_id", userId)
      .single()

    if (error && error.code !== "PGRST116") {
      console.error("Error fetching shareData:", error)
    } else if (data) {
      setShareData(data.share_data)
    }
  }, [userId])

  useEffect(() => {
    fetchShareData()
  }, [fetchShareData])

  // Update shareData in Supabase
  const handleShareDataChange = async (value: boolean) => {
    setShareData(value)
    if (!userId) return

    const { error } = await supabase
      .from("user_data_control")
      .upsert({ user_id: userId, share_data: value }, { onConflict: ["user_id"] })

    if (error) console.error("Error updating shareData:", error)
  }

  return (
    <div className="space-y-6 pb-12">
      {/* Prompt Suggestions */}
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium">Prompt suggestions</h3>
            <p className="text-muted-foreground text-xs">
              Show suggested prompts when starting a new conversation
            </p>
          </div>
          <Switch
            checked={preferences.promptSuggestions}
            onCheckedChange={setPromptSuggestions}
          />
        </div>
      </div>

      {/* Tool Invocations */}
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium">Tool invocations</h3>
            <p className="text-muted-foreground text-xs">
              Show tool execution details in conversations
            </p>
          </div>
          <Switch
            checked={preferences.showToolInvocations}
            onCheckedChange={setShowToolInvocations}
          />
        </div>
      </div>

      {/* Conversation Previews */}
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium">Conversation previews</h3>
            <p className="text-muted-foreground text-xs">
              Show conversation previews in history
            </p>
          </div>
          <Switch
            checked={preferences.showConversationPreviews}
            onCheckedChange={setShowConversationPreviews}
          />
        </div>
      </div>

      {/* Data Control (Share Data) */}
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium">Share data to improve AI</h3>
            <p className="text-muted-foreground text-xs">
              Allow your data to be used to improve AI responses
            </p>
          </div>
          <Switch checked={shareData} onCheckedChange={handleShareDataChange} />
        </div>
      </div>
    </div>
  )
}
