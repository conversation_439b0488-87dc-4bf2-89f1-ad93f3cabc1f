import Image from "next/image"
import { useState } from "react"
import { addUTM, getFavicon, getSiteName } from "./utils"
import { ExternalLink, X } from "lucide-react"

type ImageResult = {
  title: string
  imageUrl: string
  sourceUrl: string
}

export function SearchImages({ results }: { results: ImageResult[] }) {
  const [hiddenIndexes, setHiddenIndexes] = useState<Set<number>>(new Set())
  const [openImage, setOpenImage] = useState<ImageResult | null>(null)

  const handleError = (index: number) => {
    setHiddenIndexes((prev) => new Set(prev).add(index))
  }

  if (!results?.length) return null

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
      {results.map((img, i) => {
        const favicon = getFavicon(img.sourceUrl)
        return hiddenIndexes.has(i) ? null : (
          <div
            key={i}
            className="group/image relative block cursor-pointer overflow-hidden rounded-xl"
            onClick={() => setOpenImage(img)} // 👉 open in popup
          >
            <img
              src={img.imageUrl}
              alt={img.title}
              onError={() => handleError(i)}
              onLoad={(e) => e.currentTarget.classList.remove("opacity-0")}
              className="h-full max-h-48 min-h-40 w-full object-cover opacity-0 transition-opacity duration-150 ease-out"
            />
            <div className="bg-primary absolute right-0 bottom-0 left-0 flex flex-col gap-0.5 px-2.5 py-1.5 opacity-0 transition-opacity duration-100 ease-out group-hover/image:opacity-100">
              <div className="flex items-center gap-1">
                {favicon && (
                  <img
                    src={favicon}
                    alt="favicon"
                    className="h-4 w-4 rounded-full"
                  />
                )}
                <span className="text-secondary line-clamp-1 text-xs">
                  {getSiteName(img.sourceUrl)}
                </span>
              </div>
              <span className="text-secondary line-clamp-1 text-xs">
                {img.title}
              </span>
            </div>
          </div>
        )
      })}

      {/* Popup Modal */}
      {openImage && (
        <div className="fixed inset-0 z-10000 flex items-center justify-center backdrop-blur-3xl p-4 h-full">
          <div className="relative w-full  rounded-xl p-4 h-full flex items-center justify-center">
            <button
              onClick={() => setOpenImage(null)}
              className="absolute right-3 top-3 rounded-full bg-black/80 px-4 py-4 text-xs text-white hover:bg-black"
            >
              <X />
            </button>
            <button
              onClick={() => window.open(addUTM(openImage.sourceUrl), "_blank")}
              className="absolute right-20 top-3 rounded-full bg-black/80 px-4 py-4 text-xs text-white hover:bg-black"
            >
              <ExternalLink />
            </button>


            <img
              src={openImage.imageUrl}
              alt={openImage.title}
              className="h-[70vh] w-auto rounded-lg object-contain"
            />
          </div>
        </div>
      )}
    </div>
  )
}
