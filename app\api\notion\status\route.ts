import { NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/client';
import { isSupabaseEnabled } from '@/lib/supabase/config';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get('userId');

    if (!userId) {
      return new Response(JSON.stringify({ error: 'User ID required' }), { 
        status: 400 
      });
    }

    if (!isSupabaseEnabled) {
      return new Response(JSON.stringify({ 
        linked: false, 
        error: 'Database not configured' 
      }), { status: 500 });
    }

    const supabase = createClient();
    if (!supabase) {
      return new Response(JSON.stringify({ 
        linked: false, 
        error: 'Database client not available' 
      }), { status: 500 });
    }

    // Check if user has Notion integration
    const { data: notionAuth } = await supabase
      .from('notion_auth')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (notionAuth && notionAuth.access_token) {
      // Verify token is still valid by making a test API call
      try {
        const response = await fetch('https://api.notion.com/v1/users/me', {
          headers: {
            'Authorization': `Bearer ${notionAuth.access_token}`,
            'Notion-Version': '2022-06-28',
          },
        });

        if (response.ok) {
          const userData = await response.json();
          return new Response(JSON.stringify({
            linked: true,
            workspaceName: userData.name || notionAuth.workspace_name || 'Personal Workspace',
            lastSync: notionAuth.updated_at,
          }), { status: 200 });
        }
      } catch (error) {
        console.error('Notion API test failed:', error);
      }
    }

    return new Response(JSON.stringify({
      linked: false,
      loginUrl: `/api/notion/login?userId=${userId}`,
    }), { status: 200 });

  } catch (error) {
    console.error('Notion status error:', error);
    return new Response(JSON.stringify({ 
      linked: false, 
      error: 'Failed to check status' 
    }), { status: 500 });
  }
}
