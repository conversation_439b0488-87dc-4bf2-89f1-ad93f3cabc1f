"use client"

import { useEffect, useSearchParams } from 'next/navigation'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Check, ArrowLeft, BookOpen, MusicNote } from "@phosphor-icons/react"
import { useRouter } from 'next/navigation'

export default function AuthSuccess() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const message = searchParams.get('message')
  const workspace = searchParams.get('workspace')
  const service = searchParams.get('service')

  const getServiceIcon = () => {
    switch(service) {
      case 'notion':
        return <BookOpen className="w-8 h-8 text-blue-600" />
      case 'spotify':
        return <MusicNote className="w-8 h-8 text-green-600" />
      default:
        return <Check className="w-8 h-8 text-green-600" />
    }
  }

  const getServiceName = () => {
    switch(service) {
      case 'notion':
        return 'Notion'
      case 'spotify':
        return 'Spotify'
      default:
        return 'Service'
    }
  }

  // Auto-close and redirect after 3 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      // If opened in popup, close it
      if (window.opener) {
        window.opener.postMessage({ 
          type: `${service?.toUpperCase()}_AUTH_SUCCESS`,
          workspace: workspace 
        }, '*')
        window.close()
      } else {
        // If opened in same tab, redirect to home
        router.push('/')
      }
    }, 3000)

    return () => clearTimeout(timer)
  }, [router, service, workspace])

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            {getServiceIcon()}
          </div>
          <CardTitle className="text-2xl font-bold text-green-800">
            Connection Successful! 🎉
          </CardTitle>
          <CardDescription className="text-gray-600">
            {message || `${getServiceName()} has been connected successfully`}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {workspace && (
            <div className="bg-blue-50 p-3 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Workspace:</strong> {decodeURIComponent(workspace)}
              </p>
            </div>
          )}
          
          <div className="text-center text-sm text-gray-500">
            Redirecting automatically in 3 seconds...
          </div>

          <div className="flex gap-2">
            <Button 
              variant="outline" 
              className="flex-1"
              onClick={() => window.opener ? window.close() : router.push('/')}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Close
            </Button>
            <Button 
              className="flex-1 bg-green-600 hover:bg-green-700"
              onClick={() => window.opener ? window.close() : router.push('/settings')}
            >
              Go to Settings
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
