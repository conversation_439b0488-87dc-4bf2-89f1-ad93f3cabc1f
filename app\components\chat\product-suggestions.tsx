import Image from "next/image"
import { useState } from "react"
import { addUTM, getFavicon, getSiteName } from "./utils"
import {
  Sheet,
  <PERSON>et<PERSON>ontent,
  SheetDescription,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>ger,
} from "@/components/ui/sheet"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ExternalLink, Eye, Star, ShoppingCart, Brain } from "lucide-react" // ✅ Added Brain icon

type ProductResult = {
  name: string
  price?: string
  originalPrice?: string
  discount?: string
  link: string
  image?: string
  description?: string
  platform?: string
  rating?: string
  affiliateLink?: string
  searchRound?: number
  searchQuery?: string
  specifications?: string[]
  features?: string[]
  brand?: string
  model?: string
}

// Helper function to add Flipkart affiliate parameters
const addFlipkartAffiliate = (url: string): string => {
  if (url.includes('flipkart.com')) {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}affid=6e80384ac6764f619e4ccab4819d3bf1&affExtParam1=product_suggestion`;
  }
  return url;
};

// Helper function to detect platform
const getPlatform = (url: string): string => {
  if (url.includes('flipkart.com')) return 'Flipkart';
  if (url.includes('amazon.')) return 'Amazon';
  if (url.includes('myntra.com')) return 'Myntra';
  if (url.includes('ajio.com')) return 'Ajio';
  if (url.includes('nykaa.com')) return 'Nykaa';
  return 'Other';
};

// Helper function to format price properly
const formatPrice = (price: string | number | object | undefined): string => {
  if (!price || price === 'undefined' || price === 'null') {
    return '';
  }

  if (typeof price === 'string') {
    if (price === 'Price not available' || price === 'undefined' || price === 'null' || price.includes('NaN')) {
      return '';
    }

    if (price.includes('₹') || price.includes('Rs') || price.includes('$')) {
      if (price.includes('NaN')) {
        return '';
      }
      return price;
    }

    const numericMatch = price.match(/[\d,]+\.?\d*/);
    if (numericMatch) {
      const cleanNumber = numericMatch[0].replace(/,/g, '');
      const numericPrice = parseFloat(cleanNumber);
      if (!isNaN(numericPrice) && numericPrice > 0) {
        return `₹${numericPrice.toLocaleString('en-IN')}`;
      }
    }

    return '';
  }

  if (typeof price === 'number' && !isNaN(price) && price > 0) {
    return `₹${price.toLocaleString('en-IN')}`;
  }

  if (typeof price === 'object' && price !== null) {
    const priceObj = price as any;
    const possibleKeys = ['value', 'amount', 'price', 'cost', 'selling_price', 'mrp'];
    for (const key of possibleKeys) {
      if (priceObj[key] !== undefined && priceObj[key] !== null) {
        return formatPrice(priceObj[key]);
      }
    }
    return '';
  }

  return '';
};

// Calculate discount percentage
const calculateDiscount = (originalPrice: string, currentPrice: string): string => {
  const original = parseFloat(originalPrice.replace(/[^\d.]/g, ''));
  const current = parseFloat(currentPrice.replace(/[^\d.]/g, ''));

  if (original && current && original > current) {
    const discount = Math.round(((original - current) / original) * 100);
    return `${discount}% OFF`;
  }
  return '';
};

// ✅ Updated component props to include onKnowMore
export function ProductSuggestions({
  results,
  onKnowMore
}: {
  results: ProductResult[]
  onKnowMore?: (productName: string) => void // ✅ Added onKnowMore prop
}) {
  const [hiddenIndexes, setHiddenIndexes] = useState<Set<number>>(new Set())
  const [selectedProduct, setSelectedProduct] = useState<ProductResult | null>(null)
  const [isSheetOpen, setIsSheetOpen] = useState(false)

  const handleError = (index: number) => {
    setHiddenIndexes((prev) => new Set(prev).add(index))
  }

  const openProductDetails = (product: ProductResult, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setSelectedProduct(product)
    setIsSheetOpen(true)
  }

  const handleBuyNow = () => {
    if (selectedProduct) {
      const finalLink = addFlipkartAffiliate(selectedProduct.affiliateLink || selectedProduct.link)
      window.open(addUTM(finalLink), '_blank', 'noopener,noreferrer')
      setIsSheetOpen(false)
    }
  }

  // ✅ Added Know More handler
  const handleKnowMore = () => {
    if (selectedProduct && onKnowMore) {
      onKnowMore(selectedProduct.name)
      setIsSheetOpen(false)
    }
  }

  if (!results?.length) return null

  return (
    <>
      <div className="my-4 space-y-4">
        <h3 className="text-lg font-medium text-foreground">Product Suggestions</h3>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {results.map((product, i) => {
            const favicon = getFavicon(product.link)
            const platform = getPlatform(product.link)
            const finalLink = addFlipkartAffiliate(product.affiliateLink || product.link)

            return hiddenIndexes.has(i) ? null : (
              <div
                key={i}
                className="group/product relative overflow-hidden rounded-xl border border-border bg-card hover:bg-accent/50 transition-colors cursor-pointer"
              >
                {/* Product Image */}
                {product.image && (
                  <div className="relative h-48 w-full overflow-hidden">
                    <img
                      src={product.image}
                      alt={product.name}
                      onError={() => handleError(i)}
                      onLoad={(e) => e.currentTarget.classList.remove("opacity-0")}
                      className="object-cover opacity-0 transition-opacity duration-150 ease-out w-full h-full "
                    />
                    {/* Overlay with action buttons */}
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover/product:opacity-100 transition-opacity flex items-center justify-center gap-2">
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={(e) => openProductDetails(product, e)}
                        className="text-xs"
                      >
                        <Eye className="w-3 h-3 mr-1" />
                        Details
                      </Button>
                    </div>
                  </div>
                )}

                {/* Product Info */}
                <div className="p-4" onClick={(e) => openProductDetails(product, e)}>
                  <div className="flex items-start justify-between gap-2 mb-2">
                    <h4 className="font-medium text-foreground line-clamp-2 text-sm">
                      {product.name}
                    </h4>
                    <div className="flex flex-col items-end gap-1">
                      {product.price && formatPrice(product.price) && !formatPrice(product.price).includes('NaN') && (
                        <span className="text-primary font-bold text-sm whitespace-nowrap">
                          {formatPrice(product.price)}
                        </span>
                      )}
                      {product.originalPrice && formatPrice(product.originalPrice) && !formatPrice(product.originalPrice).includes('NaN') && product.originalPrice !== product.price && (
                        <span className="text-muted-foreground line-through text-xs">
                          {formatPrice(product.originalPrice)}
                        </span>
                      )}
                      {product.discount && (
                        <span className="text-green-600 font-medium text-xs">
                          {product.discount}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Platform Badge */}
                  {platform && (
                    <div className="mb-2">
                      <Badge variant={platform === 'Flipkart' ? 'default' : 'secondary'} className="text-xs">
                        {platform}
                        {platform === 'Flipkart' && ' 🛒'}
                        {platform === 'Amazon' && ' 📦'}
                      </Badge>
                    </div>
                  )}

                  {product.rating && (
                    <div className="mb-2 flex items-center gap-1">
                      <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs text-muted-foreground">
                        {product.rating}
                      </span>
                    </div>
                  )}

                  {product.description && (
                    <p className="text-muted-foreground text-xs line-clamp-2 mb-3">
                      {product.description}
                    </p>
                  )}

                  {/* Source Info */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      {favicon && (
                        <img
                          src={favicon}
                          alt="favicon"
                          width={16}
                          height={16}
                          className="rounded-full"
                        />
                      )}
                      <span className="text-muted-foreground line-clamp-1 text-xs">
                        {getSiteName(product.link)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Product Details Sheet */}
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetContent className="w-full sm:max-w-lg overflow-y-auto px-4">
          {selectedProduct && (
            <>


              <div className="mt-15 space-y-3">
                {/* Product Image */}
                {selectedProduct.image && (
                  <div className="relative h-64 w-full overflow-hidden rounded-lg bg-muted">
                    <img
                      src={selectedProduct.image}
                      alt={selectedProduct.name}
                      className="object-cover w-full h-full"
                    />
                  </div>
                )}

                <div>
                  <SheetTitle className="text-left text-lg">
                    {selectedProduct.name}
                  </SheetTitle>
                  <SheetDescription className="text-left">
                    Product details and specifications
                  </SheetDescription>
                </div>
                
                {/* Price Section */}
                <div className="space-y-2">
                  <div className="flex items-center gap-3">
                    {selectedProduct.price && formatPrice(selectedProduct.price) && (
                      <span className="text-2xl font-bold text-primary">
                        {formatPrice(selectedProduct.price)}
                      </span>
                    )}
                    {selectedProduct.originalPrice &&
                      formatPrice(selectedProduct.originalPrice) &&
                      selectedProduct.originalPrice !== selectedProduct.price && (
                        <span className="text-lg text-muted-foreground line-through">
                          {formatPrice(selectedProduct.originalPrice)}
                        </span>
                      )}
                  </div>

                  {selectedProduct.originalPrice &&
                    selectedProduct.price &&
                    formatPrice(selectedProduct.originalPrice) &&
                    formatPrice(selectedProduct.price) && (
                      <Badge variant="destructive" className="w-fit">
                        {calculateDiscount(
                          formatPrice(selectedProduct.originalPrice),
                          formatPrice(selectedProduct.price)
                        )}
                      </Badge>
                    )}
                </div>

                <Separator />

                {/* Platform & Rating */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">Platform:</span>
                    <Badge variant={getPlatform(selectedProduct.link) === 'Flipkart' ? 'default' : 'secondary'}>
                      {getPlatform(selectedProduct.link)}
                    </Badge>
                  </div>

                  {selectedProduct.rating && (
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm font-medium">{selectedProduct.rating}</span>
                    </div>
                  )}
                </div>

                {/* Description */}
                {selectedProduct.description && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Description</h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {selectedProduct.description}
                    </p>
                  </div>
                )}

                {/* Brand & Model */}
                {(selectedProduct.brand || selectedProduct.model) && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Product Info</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      {selectedProduct.brand && (
                        <div>
                          <span className="text-muted-foreground">Brand:</span>
                          <span className="ml-2 font-medium">{selectedProduct.brand}</span>
                        </div>
                      )}
                      {selectedProduct.model && (
                        <div>
                          <span className="text-muted-foreground">Model:</span>
                          <span className="ml-2 font-medium">{selectedProduct.model}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Specifications */}
                {selectedProduct.specifications && selectedProduct.specifications.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Specifications</h4>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      {selectedProduct.specifications.map((spec, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <span className="w-1 h-1 rounded-full bg-muted-foreground mt-2 flex-shrink-0" />
                          <span>{spec}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Features */}
                {selectedProduct.features && selectedProduct.features.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Key Features</h4>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      {selectedProduct.features.map((feature, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <span className="w-1 h-1 rounded-full bg-green-500 mt-2 flex-shrink-0" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* ✅ Updated Action Buttons with Know More button */}
                <div className="flex gap-2 pt-4">
                  <Button
                    onClick={handleKnowMore}
                    variant="outline"
                    className="flex-1"
                    disabled={!onKnowMore}
                  >
                    <Brain className="w-4 h-4 mr-2" />
                    Know More
                  </Button>
                  <Button onClick={handleBuyNow} className="flex-1">
                    <ShoppingCart className="w-4 h-4 mr-2" />
                    Buy Now
                  </Button>
                </div>
              </div>
            </>
          )}
        </SheetContent>
      </Sheet>
    </>
  )
}
