{"name": "nity<PERSON>a", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "devs": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.10", "@ai-sdk/gateway": "^1.0.15", "@ai-sdk/google": "^1.2.22", "@ai-sdk/mistral": "^1.2.0", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/perplexity": "^1.1.9", "@ai-sdk/xai": "^1.2.16", "@google/genai": "^1.15.0", "@google/generative-ai": "^0.24.1", "@notionhq/client": "^4.0.2", "@openrouter/ai-sdk-provider": "^0.7.1", "@phosphor-icons/react": "^2.1.7", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.6", "@sparticuz/chromium-min": "^138.0.2", "@supabase/ssr": "^0.5.2", "@tanstack/react-query": "^5.80.6", "@types/leaflet": "^1.9.20", "@vercel/analytics": "^1.5.0", "ai": "^4.3.19", "axios": "^1.11.0", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "crawler": "^2.0.2", "dompurify": "^3.2.5", "embla-carousel-react": "^8.6.0", "exa-js": "^1.6.13", "expo-server-sdk": "^2.3.3", "file-type": "^20.4.1", "howler": "^2.2.4", "idb-keyval": "^6.2.1", "imap": "^0.8.19", "jsdom": "^26.0.0", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "linkedin-jobs-api": "^1.0.7", "lucide-react": "^0.503.0", "mailparser": "^3.7.4", "marked": "^15.0.11", "motion": "^12.23.12", "mysql2": "^3.14.3", "next": "^15.4.0-canary.47", "next-themes": "^0.4.6", "nodemailer": "^7.0.6", "postcss": "^8.5.3", "puppeteer-core": "^24.16.2", "react": "^19.0.0", "react-bootstrap": "^2.10.10", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "recharts": "^2.15.4", "redis": "^5.8.2", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "shiki": "^3.4.0", "slugify": "^1.6.6", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "use-stick-to-bottom": "^1.1.1", "vaul": "^1.1.2", "zod": "^3.20.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@next/bundle-analyzer": "^15.2.4", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@tanstack/eslint-plugin-query": "^5.78.0", "@types/howler": "^2.2.12", "@types/imap": "^0.8.42", "@types/jsdom": "^21.1.7", "@types/mailparser": "^3.4.6", "@types/node": "^20", "@types/nodemailer": "^7.0.1", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.2", "prettier": "^3.5.1", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.5", "tw-animate-css": "^1.2.9", "typescript": "5.9.2"}}