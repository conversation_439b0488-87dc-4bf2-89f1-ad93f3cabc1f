"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "@/components/ui/toast"
import { createClient } from "@/lib/supabase/client"
import { isSupabaseEnabled } from "@/lib/supabase/config"
import { <PERSON>t<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Spinner } from "@phosphor-icons/react"
import { AnimatePresence, motion, useReducedMotion } from "motion/react"
import { useState } from "react"

const TRANSITION_CONTENT = {
  type: "spring",
  stiffness: 300,
  damping: 30,
}

const SPRING_CONFIG = {
  type: "spring",
  stiffness: 400,
  damping: 30,
}

// Enhanced emoji feedback options with better animations
const EMOJI_RATINGS = [
  { emoji: "😞", label: "Bad", value: 1, color: "bg-red-50 border-red-200 dark:bg-red-950" },
  { emoji: "😐", label: "Okay", value: 2, color: "bg-orange-50 border-orange-200 dark:bg-orange-950" },
  { emoji: "😊", label: "Good", value: 3, color: "bg-green-50 border-green-200 dark:bg-green-950" },
  { emoji: "😍", label: "Excellent", value: 4, color: "bg-purple-50 border-purple-200 dark:bg-purple-950" },
]

type FeedbackFormProps = {
  authUserId?: string
  onClose: () => void
}

const getErrorMessage = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message
  }
  if (typeof error === 'string') {
    return error
  }
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message)
  }
  if (error && typeof error === 'object' && 'details' in error) {
    return String(error.details)
  }
  return 'An unexpected error occurred'
}

export function FeedbackForm({ authUserId, onClose }: FeedbackFormProps) {
  const [status, setStatus] = useState<"idle" | "submitting" | "success" | "error">("idle")
  const [feedback, setFeedback] = useState("")
  const [selectedRating, setSelectedRating] = useState<number | null>(null)
  const shouldReduceMotion = useReducedMotion()

  if (!isSupabaseEnabled) {
    return null
  }

  const handleClose = () => {
    setFeedback("")
    setSelectedRating(null)
    setStatus("idle")
    onClose()
  }

  const selectedEmojiData = EMOJI_RATINGS.find(rating => rating.value === selectedRating)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!authUserId) {
      toast({
        title: "Please login to submit feedback",
        status: "error",
      })
      return
    }

    if (!selectedRating && !feedback.trim()) {
      toast({
        title: "Please select a rating or provide feedback",
        status: "error",
      })
      return
    }

    setStatus("submitting")

    try {
      const supabase = createClient()

      if (!supabase) {
        toast({
          title: "Feedback is not supported in this deployment",
          status: "info",
        })
        setStatus("idle")
        return
      }

      const { error } = await supabase.from("feedback").insert({
        message: feedback.trim() || "No additional feedback provided",
        rating: selectedRating,
        emoji: selectedEmojiData?.emoji || null,
        user_id: authUserId,
      })

      if (error) {
        console.error('Supabase error:', error)
        toast({
          title: `Error submitting feedback: ${getErrorMessage(error)}`,
          status: "error",
        })
        setStatus("error")
        return
      }

      await new Promise((resolve) => setTimeout(resolve, 1200))
      setStatus("success")

      setTimeout(() => {
        handleClose()
      }, 3000)
    } catch (error) {
      console.error('Unexpected error:', error)
      toast({
        title: `Error submitting feedback: ${getErrorMessage(error)}`,
        status: "error",
      })
      setStatus("error")
    }
  }

  // Animation variants for better control
  const containerVariants = {
    hidden: { 
      opacity: 0, 
      y: shouldReduceMotion ? 0 : 20, 
      filter: shouldReduceMotion ? "none" : "blur(4px)" 
    },
    visible: { 
      opacity: 1, 
      y: 0, 
      filter: "blur(0px)",
      transition: shouldReduceMotion ? { duration: 0.2 } : TRANSITION_CONTENT
    },
    exit: { 
      opacity: 0, 
      y: shouldReduceMotion ? 0 : -20, 
      filter: shouldReduceMotion ? "none" : "blur(4px)",
      transition: { duration: 0.15 }
    }
  }

  const emojiButtonVariants = {
    idle: { scale: 1 },
    hover: { 
      scale: shouldReduceMotion ? 1 : 1.1, 
      transition: SPRING_CONFIG
    },
    tap: { 
      scale: shouldReduceMotion ? 1 : 0.95,
      transition: { duration: 0.1 }
    },
    selected: { 
      scale: shouldReduceMotion ? 1 : 1.05,
      transition: SPRING_CONFIG
    }
  }

  const successVariants = {
    hidden: { 
      opacity: 0, 
      scale: shouldReduceMotion ? 1 : 0.8, 
      y: shouldReduceMotion ? 0 : 20 
    },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: {
        ...SPRING_CONFIG,
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }

  const successItemVariants = {
    hidden: { 
      opacity: 0, 
      y: shouldReduceMotion ? 0 : 10 
    },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: SPRING_CONFIG
    }
  }

  return (
    <div className="h-[280px] w-full p-3">
      <AnimatePresence mode="wait">
        {status === "success" ? (
          <motion.div
            key="success"
            className="flex h-[280px] w-full flex-col items-center justify-center"
            variants={successVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            <motion.div
              variants={successItemVariants}
              className="rounded-full bg-green-500/10 p-3 mb-2"
              whileHover={shouldReduceMotion ? {} : { 
                scale: 1.1, 
                rotate: [0, 5, -5, 0],
                transition: { duration: 0.4 }
              }}
            >
              <SealCheck className="size-8 text-green-500" />
            </motion.div>

            <motion.p 
              variants={successItemVariants}
              className="text-foreground mt-2 mb-1 text-center text-lg font-semibold"
            >
              Thank you for your time!
            </motion.p>

            <motion.p 
              variants={successItemVariants}
              className="text-muted-foreground text-sm text-center"
            >
              Your feedback makes Nityasha better.
            </motion.p>

            {selectedEmojiData && (
              <motion.div
                variants={successItemVariants}
                className="mt-4 flex items-center gap-2 px-4 py-2 rounded-full bg-gray-50 dark:bg-gray-800"
                whileHover={shouldReduceMotion ? {} : { scale: 1.05 }}
              >
                <span className="text-2xl">{selectedEmojiData.emoji}</span>
                <span className="text-muted-foreground text-sm font-medium">
                  {selectedEmojiData.label}
                </span>
              </motion.div>
            )}
          </motion.div>
        ) : (
          <motion.form
            key="form"
            className="flex h-full flex-col"
            onSubmit={handleSubmit}
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            {/* Enhanced Emoji Rating Section */}
            <motion.div 
              className="mb-4"
              initial={{ opacity: 0, y: shouldReduceMotion ? 0 : -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1, ...SPRING_CONFIG }}
            >
              <p className="text-foreground text-sm font-medium mb-3">
                How was your experience?
              </p>
              <div className="flex gap-2 justify-center">
                {EMOJI_RATINGS.map((rating, index) => (
                  <motion.button
                    key={rating.value}
                    type="button"
                    onClick={() => setSelectedRating(rating.value === selectedRating ? null : rating.value)}
                    className={`flex flex-col items-center p-3 rounded-xl border-2 transition-colors duration-200 w-[5.5rem] ${
                      selectedRating === rating.value
                        ? `${rating.color} border-current shadow-sm`
                        : "border-gray-200 hover:border-gray-300 dark:border-gray-700 dark:hover:border-gray-600"
                    }`}
                    disabled={status === "submitting"}
                    variants={emojiButtonVariants}
                    initial="idle"
                    whileHover="hover"
                    whileTap="tap"
                    animate={selectedRating === rating.value ? "selected" : "idle"}
                    custom={index}
                  >
                    <motion.span 
                      className="text-2xl mb-1"
                      animate={selectedRating === rating.value && !shouldReduceMotion ? {
                        rotate: [0, -10, 10, 0],
                        transition: { duration: 0.5 }
                      } : {}}
                    >
                      {rating.emoji}
                    </motion.span>
                    <span className="text-xs text-muted-foreground font-medium">
                      {rating.label}
                    </span>
                  </motion.button>
                ))}
              </div>
            </motion.div>

            {/* Enhanced Text Feedback Section */}
            <motion.div 
              className="flex-1 relative"
              initial={{ opacity: 0, y: shouldReduceMotion ? 0 : 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, ...SPRING_CONFIG }}
            >
              <motion.span
                aria-hidden="true"
                className="text-muted-foreground pointer-events-none absolute top-3.5 left-4 text-sm leading-[1.4] select-none"
                animate={{
                  opacity: feedback ? 0 : 1,
                  y: feedback ? -5 : 0,
                }}
                transition={{ duration: 0.2, ease: "easeOut" }}
              >
                Tell us more about your experience... (optional)
              </motion.span>
              <motion.textarea
                className="text-foreground h-full w-full resize-none rounded-lg bg-transparent px-4 py-3.5 text-sm outline-none border border-gray-200 dark:border-gray-700 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 dark:focus:ring-blue-900 transition-all duration-200"
                onChange={(e) => setFeedback(e.target.value)}
                disabled={status === "submitting"}
                value={feedback}
                whileFocus={shouldReduceMotion ? {} : { scale: 1.02 }}
                transition={SPRING_CONFIG}
              />
            </motion.div>

            {/* Enhanced Button Section */}
            <motion.div 
              className="flex justify-between pt-2 pr-3 pb-3 pl-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <motion.div whileHover={shouldReduceMotion ? {} : { scale: 1.05 }} whileTap={shouldReduceMotion ? {} : { scale: 0.95 }}>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleClose}
                  aria-label="Close"
                  disabled={status === "submitting"}
                  className="rounded-lg"
                >
                  <CaretLeft size={16} className="text-foreground" />
                </Button>
              </motion.div>

              <motion.div 
                whileHover={shouldReduceMotion ? {} : { scale: 1.05 }} 
                whileTap={shouldReduceMotion ? {} : { scale: 0.95 }}
              >
                <Button
                  type="submit"
                  variant="outline"
                  size="sm"
                  aria-label="Submit feedback"
                  className="rounded-lg min-w-[80px]"
                  disabled={status === "submitting" || (!selectedRating && !feedback.trim())}
                >
                  <AnimatePresence mode="wait">
                    {status === "submitting" ? (
                      <motion.span
                        key="submitting"
                        className="inline-flex items-center gap-2"
                        initial={{ opacity: 0, x: shouldReduceMotion ? 0 : 10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: shouldReduceMotion ? 0 : -10 }}
                        transition={{ duration: 0.15 }}
                      >
                        <motion.div
                          animate={shouldReduceMotion ? {} : { rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        >
                          <Spinner className="size-4" />
                        </motion.div>
                        Sending...
                      </motion.span>
                    ) : (
                      <motion.span
                        key="send"
                        initial={{ opacity: 0, x: shouldReduceMotion ? 0 : -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: shouldReduceMotion ? 0 : 10 }}
                        transition={{ duration: 0.15 }}
                      >
                        Send
                      </motion.span>
                    )}
                  </AnimatePresence>
                </Button>
              </motion.div>
            </motion.div>
          </motion.form>
        )}
      </AnimatePresence>
    </div>
  )
}
