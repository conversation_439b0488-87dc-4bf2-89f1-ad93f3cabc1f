import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import {
    Briefcase,
    MapPin,
    Calendar,
    Building,
    CurrencyDollar,
    Clock,
    Users,
    ArrowUpRight,
    WifiX
} from '@phosphor-icons/react';
import {
    Carousel,
    CarouselContent,
    CarouselItem,
    CarouselNext,
    CarouselPrevious,
    type CarouselApi,
} from '@/components/ui/carousel';

export type JobResult = {
    position: string;
    company: string;
    companyLogo?: string;
    location: string;
    date: string;
    agoTime: string;
    salary?: string;
    jobUrl: string;
    jobType?: string;
    experienceLevel?: string;
    remote?: boolean;
    description?: string;
    applicants?: string;
    employmentType?: string;
};

type JobSuggestionsProps = {
    results: JobResult[];
    onApplyJob?: (jobUrl: string, jobTitle: string) => void;
    searchMetadata?: any;
};

// Job Card Component
const JobCard = ({
    job,
    onApply,
    onViewDetails
}: {
    job: JobResult;
    onApply?: (jobUrl: string, jobTitle: string) => void;
    onViewDetails?: (job: JobResult) => void;
}) => {
    const [isExpanded, setIsExpanded] = useState(false);

    const handleApply = () => {
        if (onApply) {
            onApply(job.jobUrl, job.position);
        } else {
            window.open(job.jobUrl, '_blank');
        }
    };

    const getSalaryDisplay = () => {
        if (job.salary && job.salary.trim() !== '') {
            return job.salary;
        }
        return 'Salary not disclosed';
    };

    const getJobTypeColor = (type?: string) => {
        switch (type?.toLowerCase()) {
            case 'full time':
                return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
            case 'part time':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
            case 'contract':
                return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
            case 'internship':
                return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
        }
    };

    const getExperienceColor = (level?: string) => {
        switch (level?.toLowerCase()) {
            case 'entry level':
                return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400';
            case 'mid level':
                return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
            case 'senior level':
                return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
            default:
                return 'bg-slate-100 text-slate-800 dark:bg-slate-900/20 dark:text-slate-400';
        }
    };

    return (
        <div className="group relative overflow-hidden rounded-xl border bg-card p-6 shadow-sm transition-all hover:shadow-md hover:border-primary/20">
            {/* Header */}
            <div className="flex items-start gap-4 mb-4">
                <div className="relative">
                    {job.companyLogo ? (
                        <img
                            src={job.companyLogo}
                            alt={`${job.company} logo`}
                            className="size-12 rounded-lg object-cover border bg-muted"
                        />
                    ) : (
                        <div className="size-12 rounded-lg bg-muted flex items-center justify-center">
                            <Building className="size-6 text-muted-foreground" />
                        </div>
                    )}
                    {job.remote && (
                        <div className="absolute -top-1 -right-1 size-4 bg-green-500 rounded-full flex items-center justify-center">
                            <WifiX className="size-2 text-white" />
                        </div>
                    )}
                </div>

                <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-lg mb-1 line-clamp-2 group-hover:text-primary transition-colors">
                        {job.position}
                    </h3>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                        <Building className="size-4" />
                        <span className="font-medium">{job.company}</span>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                            <MapPin className="size-4" />
                            <span>{job.location}</span>
                        </div>
                        <div className="flex items-center gap-1">
                            <Clock className="size-4" />
                            <span>{job.agoTime}</span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-4">
                {job.jobType && (
                    <span className={cn(
                        "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                        getJobTypeColor(job.jobType)
                    )}>
                        <Briefcase className="size-3 mr-1" />
                        {job.jobType}
                    </span>
                )}
                {job.experienceLevel && (
                    <span className={cn(
                        "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                        getExperienceColor(job.experienceLevel)
                    )}>
                        <Users className="size-3 mr-1" />
                        {job.experienceLevel}
                    </span>
                )}
                {job.remote && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400">
                        <WifiX className="size-3 mr-1" />
                        Remote
                    </span>
                )}
                {job.applicants && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400">
                        <Users className="size-3 mr-1" />
                        {job.applicants} applicants
                    </span>
                )}
            </div>

            {/* Salary */}
            <div className="flex items-center gap-2 mb-4 p-3 bg-muted/30 rounded-lg">
                <CurrencyDollar className="size-5 text-green-600" />
                <div>
                    <div className="font-medium text-sm">{getSalaryDisplay()}</div>
                    <div className="text-xs text-muted-foreground">
                        {job.employmentType || 'Employment type not specified'}
                    </div>
                </div>
            </div>

            {/* Description Preview */}
            {job.description && (
                <div className="mb-4">
                    <p className={cn(
                        "text-sm text-muted-foreground leading-relaxed",
                        isExpanded ? "" : "line-clamp-2"
                    )}>
                        {job.description}
                    </p>
                    {job.description.length > 100 && (
                        <button
                            onClick={() => setIsExpanded(!isExpanded)}
                            className="text-xs text-primary hover:underline mt-1 font-medium"
                        >
                            {isExpanded ? "Show less" : "Show more"}
                        </button>
                    )}
                </div>
            )}

            {/* Actions */}
            <div className="flex gap-2 pt-4 border-t">
                <button
                    onClick={handleApply}
                    className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors text-sm"
                >
                    <ArrowUpRight className="size-4" />
                    Apply Now
                </button>
                <button
                    onClick={() => onViewDetails?.(job)}
                    className="px-4 py-2 border border-border rounded-lg font-medium hover:bg-muted transition-colors text-sm"
                >
                    View Details
                </button>
            </div>

            {/* Posted Date */}
            <div className="absolute top-4 right-4 flex items-center gap-1 text-xs text-muted-foreground bg-muted/80 px-2 py-1 rounded-full">
                <Calendar className="size-3" />
                {job.date}
            </div>
        </div>
    );
};

// Main Job Suggestions Component
export const JobSuggestions: React.FC<JobSuggestionsProps> = ({
    results,
    onApplyJob,
    searchMetadata
}) => {
    const [selectedJob, setSelectedJob] = useState<JobResult | null>(null);
    const [carouselApi, setCarouselApi] = useState<CarouselApi>();
    const [current, setCurrent] = useState(0);
    const [count, setCount] = useState(0);

    // Carousel API effects
    useEffect(() => {
        if (!carouselApi) {
            return;
        }

        setCount(carouselApi.scrollSnapList().length);
        setCurrent(carouselApi.selectedScrollSnap() + 1);

        carouselApi.on("select", () => {
            setCurrent(carouselApi.selectedScrollSnap() + 1);
        });
    }, [carouselApi]);

    const handleViewDetails = (job: JobResult) => {
        setSelectedJob(job);
    };

    const closeModal = () => {
        setSelectedJob(null);
    };

    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                    <Briefcase className="size-5 text-primary" />
                    <h3 className="font-semibold text-lg">Job Suggestions</h3>
                    <span className="text-sm text-muted-foreground">
                        ({results.length} jobs found)
                    </span>
                </div>
                {searchMetadata && (
                    <div className="text-xs text-muted-foreground">
                        {searchMetadata.searchQuery && `Query: "${searchMetadata.searchQuery}"`}
                    </div>
                )}
            </div>

            {/* Carousel Implementation */}
            <div className="relative">
                <Carousel
                    setApi={setCarouselApi}
                    opts={{
                        align: "start",
                        loop: false,
                    }}
                    className="w-full"
                >
                    <CarouselContent className="">
                        {results.map((job, index) => (
                            <CarouselItem
                                key={`${job.jobUrl}-${index}`}
                                className="pl-2 md:pl-4 basis-full md:basis-1/2 lg:basis-1/2 xl:basis-1/2"
                            // ↑ Change: xl:basis-1/3 को xl:basis-1/2 kiya
                            >
                                <JobCard
                                    job={job}
                                    onApply={onApplyJob}
                                    onViewDetails={handleViewDetails}
                                />
                            </CarouselItem>
                        ))}
                    </CarouselContent>

                    {/* Navigation Buttons */}
                    {results.length > 1 && (
                        <>
                            <CarouselPrevious className="hidden md:flex -left-12 border-2 size-10 hover:bg-primary hover:text-primary-foreground hover:border-primary transition-colors" />
                            <CarouselNext className="hidden md:flex -right-12 border-2 size-10 hover:bg-primary hover:text-primary-foreground hover:border-primary transition-colors" />
                        </>
                    )}
                </Carousel>

                {/* Carousel Indicators/Dots */}
                {results.length > 1 && (
                    <div className="flex items-center justify-center gap-2 mt-6">
                        <div className="flex items-center gap-1">
                            {Array.from({ length: count }).map((_, index) => (
                                <button
                                    key={index}
                                    onClick={() => carouselApi?.scrollTo(index)}
                                    className={cn(
                                        "size-2 rounded-full transition-all duration-300",
                                        current === index + 1
                                            ? "bg-primary w-6"
                                            : "bg-muted-foreground/30 hover:bg-muted-foreground/50"
                                    )}
                                />
                            ))}
                        </div>
                        <div className="text-xs text-muted-foreground ml-3 hidden sm:block">
                            {current} of {count}
                        </div>
                    </div>
                )}

                {/* Mobile Navigation Hint */}
                {results.length > 1 && (
                    <div className="text-center mt-3 md:hidden">
                        <p className="text-xs text-muted-foreground">
                            👈 Swipe to see more jobs 👉
                        </p>
                    </div>
                )}
            </div>

            {/* Job Details Modal */}
            {selectedJob && (
                <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
                    <div className="relative w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-card rounded-xl border shadow-2xl">
                        <div className="sticky top-0 flex items-center justify-between p-6 border-b bg-card/95 backdrop-blur-sm">
                            <h2 className="text-xl font-semibold">Job Details</h2>
                            <button
                                onClick={closeModal}
                                className="size-8 flex items-center justify-center rounded-lg hover:bg-muted transition-colors"
                            >
                                ✕
                            </button>
                        </div>

                        <div className="p-6">
                            <div className="flex items-start gap-4 mb-6">
                                {selectedJob.companyLogo ? (
                                    <img
                                        src={selectedJob.companyLogo}
                                        alt={`${selectedJob.company} logo`}
                                        className="size-16 rounded-xl object-cover border"
                                    />
                                ) : (
                                    <div className="size-16 rounded-xl bg-muted flex items-center justify-center">
                                        <Building className="size-8 text-muted-foreground" />
                                    </div>
                                )}

                                <div>
                                    <h3 className="text-2xl font-bold mb-2">{selectedJob.position}</h3>
                                    <div className="space-y-1 text-muted-foreground">
                                        <div className="flex items-center gap-2">
                                            <Building className="size-4" />
                                            <span className="font-medium">{selectedJob.company}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <MapPin className="size-4" />
                                            <span>{selectedJob.location}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="space-y-4">
                                <div>
                                    <h4 className="font-semibold mb-2">Salary</h4>
                                    <p className="text-muted-foreground">
                                        {selectedJob.salary || 'Not disclosed'}
                                    </p>
                                </div>

                                {selectedJob.description && (
                                    <div>
                                        <h4 className="font-semibold mb-2">Job Description</h4>
                                        <p className="text-muted-foreground leading-relaxed whitespace-pre-wrap">
                                            {selectedJob.description}
                                        </p>
                                    </div>
                                )}

                                <div className="flex gap-2 pt-4">
                                    <button
                                        onClick={() => {
                                            window.open(selectedJob.jobUrl, '_blank');
                                            closeModal();
                                        }}
                                        className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors"
                                    >
                                        <ArrowUpRight className="size-4" />
                                        Apply on LinkedIn
                                    </button>
                                    <button
                                        onClick={closeModal}
                                        className="px-4 py-2 border border-border rounded-lg font-medium hover:bg-muted transition-colors"
                                    >
                                        Close
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default JobSuggestions;
