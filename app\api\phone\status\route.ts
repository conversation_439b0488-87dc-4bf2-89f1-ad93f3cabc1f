import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 })
    }

    // Check Supabase for phone verification status
    const { data, error } = await supabase
      .from('phone_verifications')
      .select('phone_number, verified_at')
      .eq('user_id', userId)
      .eq('verified', true)
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error('Supabase error:', error)
      return NextResponse.json({ error: 'Database error' }, { status: 500 })
    }

    return NextResponse.json({
      linked: !!data,
      phoneNumber: data?.phone_number || undefined
    })

  } catch (error) {
    console.error('Phone status error:', error)
    return NextResponse.json({ error: 'Failed to fetch phone status' }, { status: 500 })
  }
}
