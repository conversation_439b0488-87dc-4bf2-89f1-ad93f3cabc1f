import { createOpenRouter } from "@openrouter/ai-sdk-provider"
import { ModelConfig } from "../types"

export const openrouterModels: ModelConfig[] = [
  {
    id: "openrouter:deepseek/deepseek-r1:free",
    name: "DeepSeek R1",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "OpenRouter",
    baseProviderId: "deepseek",
    description:
      "Flagship model by DeepSeek, optimized for performance and reliability.",
    tags: ["flagship", "reasoning", "performance", "reliability"],
    contextWindow: 163840,
    inputCost: 0,
    outputCost: 0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    reasoning: true,
    webSearch: false,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://openrouter.ai/",
    apiDocs: "https://openrouter.ai/deepseek/deepseek-r1:free",
    modelPage: "https://deepseek.com",
    releasedAt: "2024-04-01",
    icon: "deepseek",
    apiSdk: (apiKey?: string) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
      }).chat("deepseek/deepseek-r1:free"),
  },
  {
    id: "openrouter:anthropic/claude-sonnet-4",
    name: "Claude Sonnet 4",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "Claude",
    baseProviderId: "claude",
    description:
      "Claude's latest model with transparent reasoning mode, excellent for complex problem-solving and coding tasks.",
    tags: ["flagship", "reasoning", "transparent", "coding"],
    contextWindow: 200000,
    inputCost: 3.0,
    outputCost: 15.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/anthropic/claude-sonnet-4",
    modelPage: "https://www.anthropic.com/claude/sonnet",
    releasedAt: "2025-04-01",
    icon: "claude",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          extraBody: {
            plugins: [{ id: "web", max_results: 3 }],
          },
        }),
      }).chat("anthropic/claude-sonnet-4"),
  },
  {
    id: "openrouter:anthropic/claude-3.7-sonnet:thinking",
    name: "Claude 3.7 Sonnet (Thinking)",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "Claude",
    baseProviderId: "claude",
    description:
      "Claude's latest model with transparent reasoning mode, excellent for complex problem-solving and coding tasks.",
    tags: ["flagship", "reasoning", "transparent", "coding"],
    contextWindow: 200000,
    inputCost: 3.0,
    outputCost: 15.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/anthropic/claude-3.7-sonnet:thinking",
    modelPage: "https://www.anthropic.com/claude",
    releasedAt: "2025-02-24",
    icon: "claude",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          extraBody: {
            plugins: [{ id: "web", max_results: 3 }],
          },
        }),
      }).chat("anthropic/claude-3.7-sonnet:thinking"),
  },
  {
    id: "openrouter:google/gemini-2.5-pro-preview",
    name: "Gemini 2.5 Pro",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "Gemini",
    baseProviderId: "gemini",
    description:
      "Google's advanced multimodal model with methodical reasoning and excellent codebase analysis capabilities.",
    tags: ["flagship", "multimodal", "methodical", "coding"],
    contextWindow: 1048576,
    inputCost: 1.25,
    outputCost: 10.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: true,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Fast",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/google/gemini-2.5-pro-preview",
    modelPage: "https://ai.google.dev",
    releasedAt: "2025-03-20",
    icon: "gemini",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          extraBody: {
            plugins: [{ id: "web", max_results: 3 }],
          },
        }),
      }).chat("google/gemini-2.5-pro-preview"),
  },
  {
    id: "openrouter:openai/gpt-4.1",
    name: "GPT-4.1",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "GPT",
    baseProviderId: "openai",
    description:
      "OpenAI's latest developer-focused model with exceptional instruction following and coding capabilities.",
    tags: ["flagship", "coding", "instruction", "reasoning"],
    contextWindow: 1047576,
    inputCost: 2.0,
    outputCost: 8.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Fast",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/openai/gpt-4.1",
    modelPage: "https://openai.com",
    releasedAt: "2025-04-14",
    icon: "openai",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          extraBody: {
            plugins: [{ id: "web", max_results: 3 }],
          },
        }),
      }).chat("openai/gpt-4.1"),
  },
  {
    id: "openrouter:openai/o4-mini",
    name: "O4 Mini",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "GPT",
    baseProviderId: "openai",
    description:
      "Smaller, faster version of O4 with excellent performance-to-cost ratio.",
    tags: ["efficient", "fast", "coding", "cost-effective"],
    contextWindow: 200000,
    inputCost: 1.1,
    outputCost: 4.4,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Fast",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/openai/o4-mini",
    modelPage: "https://platform.openai.com/docs/models/o4-mini",
    releasedAt: "2025-04-01",
    icon: "openai",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          extraBody: {
            plugins: [{ id: "web", max_results: 3 }],
          },
        }),
      }).chat("openai/o4-mini"),
  },
  {
    id: "openrouter:x-ai/grok-3-mini-beta",
    name: "Grok-3-Mini-Beta",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "Grok",
    baseProviderId: "xai",
    description:
      "xAI's compact model with multi-threaded reasoning and real-time knowledge access.",
    tags: ["fast", "multi-threaded", "reasoning", "uncensored"],
    contextWindow: 131072,
    inputCost: 0.3,
    outputCost: 0.5,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Fast",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/x-ai/grok-3-mini-beta",
    modelPage: "https://x.ai/api",
    releasedAt: "2025-02-15",
    icon: "xai",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          extraBody: {
            plugins: [{ id: "web", max_results: 3 }],
          },
        }),
      }).chat("x-ai/grok-3-mini-beta"),
  },
  {
    id: "openrouter:google/gemini-2.5-flash-preview-05-20",
    name: "Gemini 2.5 Flash",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "Gemini",
    baseProviderId: "gemini",
    description:
      "High-speed variant of Gemini 2.5 optimized for responsiveness while maintaining strong reasoning abilities.",
    tags: ["fast", "responsive", "efficient", "coding"],
    contextWindow: 1048576,
    inputCost: 0.15,
    outputCost: 0.6,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Fast",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/google/gemini-2.5-flash-preview-05-20",
    modelPage: "https://ai.google.dev",
    releasedAt: "2025-03-25",
    icon: "gemini",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          extraBody: {
            plugins: [{ id: "web", max_results: 3 }],
          },
        }),
      }).chat("google/gemini-2.5-flash-preview-05-20"),
  },
  {
    id: "openrouter:meta-llama/llama-3.3-8b-instruct:free",
    name: "Llama 3.3 8B Instruct",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "Llama",
    baseProviderId: "meta",
    description:
      "Meta's latest model with improved reasoning capabilities and enhanced performance.",
    tags: ["flagship", "reasoning", "performance", "reliability"],
    contextWindow: 128000,
    inputCost: 0,
    outputCost: 0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Fast",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/meta-llama/llama-3.3-8b-instruct:free",
    modelPage: "https://www.llama.com/",
    releasedAt: "2025-04-01",
    icon: "meta",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          extraBody: {
            plugins: [{ id: "web", max_results: 3 }],
          },
        }),
      }).chat("meta-llama/llama-3.3-8b-instruct:free"),
  },
  {
    id: "openrouter:openai/gpt-4.1-mini",
    name: "GPT-4.1 Mini",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "GPT",
    baseProviderId: "openai",
    description:
      "Compact version of GPT-4.1 optimized for speed and cost-effectiveness while maintaining strong capabilities.",
    tags: ["efficient", "fast", "coding", "cost-effective"],
    contextWindow: 1047576,
    inputCost: 0.4,
    outputCost: 1.6,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Fast",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/openai/gpt-4.1-mini",
    modelPage: "https://platform.openai.com/docs/models/gpt-4o-mini",
    releasedAt: "2025-04-14",
    icon: "openai",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          extraBody: {
            plugins: [{ id: "web", max_results: 3 }],
          },
        }),
      }).chat("openai/gpt-4.1-mini"),
  },
  {
    id: "openrouter:openai/gpt-4.1-nano",
    name: "GPT-4.1 Nano",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "GPT",
    baseProviderId: "openai",
    description:
      "Ultra-efficient nano version of GPT-4.1 for rapid inference and minimal cost.",
    tags: ["ultra-fast", "nano", "efficient", "cost-effective"],
    contextWindow: 1047576,
    inputCost: 0.1,
    outputCost: 0.4,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/openai/gpt-4.1-nano",
    modelPage: "https://platform.openai.com/docs/models/gpt-4o-mini",
    releasedAt: "2025-04-14",
    icon: "openai",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          extraBody: {
            plugins: [{ id: "web", max_results: 3 }],
          },
        }),
      }).chat("openai/gpt-4.1-nano"),
  },
  {
    id: "openrouter:openai/o3-mini",
    name: "O3 Mini",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "GPT",
    baseProviderId: "openai",
    description:
      "Compact reasoning model with strong problem-solving capabilities at a lower cost.",
    tags: ["reasoning", "efficient", "problem-solving", "cost-effective"],
    contextWindow: 200000,
    inputCost: 1.1,
    outputCost: 4.4,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Fast",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/openai/o3-mini",
    modelPage: "https://platform.openai.com/docs/models/o3-mini",
    releasedAt: "2025-04-01",
    icon: "openai",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          plugins: [{ id: "web", max_results: 3 }],
        }),
      }).chat("openai/o3-mini"),
  },
  {
    id: "openrouter:anthropic/claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "Claude",
    baseProviderId: "claude",
    description:
      "Anthropic's flagship model with exceptional reasoning, coding, and creative capabilities.",
    tags: ["flagship", "reasoning", "coding", "creative"],
    contextWindow: 200000,
    inputCost: 3.0,
    outputCost: 15.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/anthropic/claude-3.5-sonnet",
    modelPage: "https://www.anthropic.com/claude",
    releasedAt: "2024-10-22",
    icon: "claude",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          plugins: [{ id: "web", max_results: 3 }],
        }),
      }).chat("anthropic/claude-3.5-sonnet"),
  },
  {
    id: "openrouter:google/gemini-2.5-flash-preview-05-20",
    name: "Gemini 2.0 Flash",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "Gemini",
    baseProviderId: "gemini",
    description:
      "Google's next-generation multimodal model with enhanced speed and reasoning capabilities.",
    tags: ["fast", "multimodal", "reasoning", "coding"],
    contextWindow: 1048576,
    inputCost: 0.1,
    outputCost: 0.4,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: true,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Fast",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/google/gemini-2.5-flash-preview-05-20",
    modelPage: "https://ai.google.dev",
    releasedAt: "2024-12-11",
    icon: "gemini",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          plugins: [{ id: "web", max_results: 3 }],
        }),
      }).chat("google/gemini-2.5-flash-preview-05-20"),
  },
  {
    id: "openrouter:google/gemini-2.0-flash-lite",
    name: "Gemini 2.0 Flash Lite",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "Gemini",
    baseProviderId: "gemini",
    description:
      "Lightweight version of Gemini 2.0 Flash optimized for speed and cost-efficiency.",
    tags: ["fast", "lite", "efficient", "cost-effective"],
    contextWindow: 1048576,
    inputCost: 0.075,
    outputCost: 0.3,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/google/gemini-2.0-flash-lite",
    modelPage: "https://ai.google.dev",
    releasedAt: "2024-12-11",
    icon: "gemini",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          plugins: [{ id: "web", max_results: 3 }],
        }),
      }).chat("google/gemini-2.0-flash-lite"),
  },
  {
    id: "openrouter:openai/gpt-4.5-preview",
    name: "GPT-4.5 Preview",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "GPT",
    baseProviderId: "openai",
    description:
      "Preview version of OpenAI's next-generation model with advanced reasoning and multimodal capabilities.",
    tags: ["preview", "advanced", "reasoning", "multimodal"],
    contextWindow: 128000,
    inputCost: 75,
    outputCost: 150,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: true,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/openai/gpt-4.5-preview",
    modelPage: "https://platform.openai.com/docs/models",
    releasedAt: "2025-05-01",
    icon: "openai",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          plugins: [{ id: "web", max_results: 3 }],
        }),
      }).chat("openai/gpt-4.5-preview"),
  },
  {
    id: "openrouter:perplexity/sonar",
    name: "Perplexity Sonar",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "Sonar",
    baseProviderId: "perplexity",
    description:
      "Sonar is a fast, affordable QA model with source control and citation support—ideal for lightweight, real-time integrations.",
    tags: ["fast", "simple", "affordable", "QA"],
    contextWindow: 127072,
    inputCost: 1,
    outputCost: 1,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/docs",
    modelPage: "https://openrouter.ai/perplexity/sonar",
    releasedAt: "2025-01-27",
    icon: "perplexity",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          plugins: [{ id: "web", max_results: 3 }],
        }),
      }).chat("perplexity/sonar"),
  },
  {
    id: "openrouter:perplexity/sonar-reasoning",
    name: "Perplexity Sonar Reasoning",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "Sonar",
    baseProviderId: "perplexity",
    description:
      "An enhanced version of Sonar optimized for deeper reasoning and more complex tasks, while retaining fast response times.",
    tags: ["reasoning", "fast", "QA", "affordable"],
    contextWindow: 127072,
    inputCost: 1,
    outputCost: 5,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/docs",
    modelPage: "https://openrouter.ai/perplexity/sonar-reasoning",
    releasedAt: "2025-01-29",
    icon: "perplexity",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          extraBody: {
            plugins: [{ id: "web", max_results: 3 }],
          },
        }),
      }).chat("perplexity/sonar-reasoning"),
  },
  {
    id: "openrouter:perplexity/sonar-reasoning-pro",
    name: "Perplexity Sonar Reasoning Pro",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "Sonar",
    baseProviderId: "perplexity",
    description:
      "Perplexity's most advanced Sonar model with pro-level reasoning, accuracy, and context handling—ideal for complex tasks.",
    tags: ["reasoning", "pro", "advanced", "QA", "research"],
    contextWindow: 127072,
    inputCost: 2,
    outputCost: 8,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/docs",
    modelPage: "https://openrouter.ai/perplexity/sonar-reasoning-pro",
    releasedAt: "2025-07-25",
    icon: "perplexity",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          extraBody: {
            plugins: [{ id: "web", max_results: 3 }],
          },
        }),
      }).chat("perplexity/sonar-reasoning-pro"),
  },
  {
    id: "openrouter:perplexity/sonar-pro",
    name: "Perplexity Sonar Pro",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "Sonar",
    baseProviderId: "perplexity",
    description:
      "A high-performance version of Sonar optimized for speed and accuracy across general tasks, with solid reasoning capabilities.",
    tags: ["fast", "accurate", "QA", "general-purpose"],
    contextWindow: 200000,
    inputCost: 3,
    outputCost: 15,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Fast",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/docs",
    modelPage: "https://openrouter.ai/perplexity/sonar-pro",
    releasedAt: "2025-03-27",
    icon: "perplexity",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          extraBody: {
            plugins: [{ id: "web", max_results: 3 }],
          },
        }),
      }).chat("perplexity/sonar-pro"),
  },
  {
    id: "openrouter:perplexity/sonar-deep-research",
    name: "Perplexity Sonar Deep Research",
    provider: "OpenRouter",
    providerId: "openrouter",
    modelFamily: "Sonar",
    baseProviderId: "perplexity",
    description:
      "Perplexity's most powerful model for deep research, long-context understanding, and advanced reasoning tasks.",
    tags: ["deep research", "advanced", "long-context", "reasoning", "QA"],
    contextWindow: 128000,
    inputCost: 2,
    outputCost: 8,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    reasoning: true,
    webSearch: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://openrouter.ai",
    apiDocs: "https://openrouter.ai/docs",
    modelPage: "https://openrouter.ai/perplexity/sonar-deep-research",
    releasedAt: "2025-03-07",
    icon: "perplexity",
    apiSdk: (apiKey?: string, opts?: { enableSearch?: boolean }) =>
      createOpenRouter({
        apiKey: apiKey || process.env.OPENROUTER_API_KEY,
        ...(opts?.enableSearch && {
          extraBody: {
            plugins: [{ id: "web", max_results: 3 }],
          },
        }),
      }).chat("perplexity/sonar-deep-research"),
  },
]
