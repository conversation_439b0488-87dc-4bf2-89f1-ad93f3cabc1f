'use client';

import dynamic from 'next/dynamic';
import React from 'react';

// Define your props interface
interface MapData {
  type: 'hotel' | 'coaching';
  coordinates: [number, number];
  popupContent: {
    name: string;
    rating?: number;
    contact?: string;
    [key: string]: any;
  };
}

interface MapDisplayProps {
  results: Array<{
    mapData: MapData;
    [key: string]: any;
  }>;
  mapCenter?: [number, number];
  height?: string;
  zoom?: number;
}

// Dynamically import MapDisplay with SSR disabled
const MapDisplay = dynamic(() => import('./MapDisplay'), {
  ssr: false,
  loading: () => (
    <div 
      className="flex items-center justify-center border rounded-lg bg-muted/30"
      style={{ height: '400px' }}
    >
      <p className="text-muted-foreground">Loading map...</p>
    </div>
  )
});

const MapWrapper: React.FC<MapDisplayProps> = (props) => {
  return <MapDisplay {...props} />;
};

export default MapWrapper;
