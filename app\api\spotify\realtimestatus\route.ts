import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { createClient } from '@supabase/supabase-js';

// Enable edge runtime for streaming
export const runtime = 'edge';
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const userId = searchParams.get('userId');
  const stream = searchParams.get('stream') === 'true';

  if (!userId) {
    return NextResponse.json({ error: 'Missing userId' }, { status: 400 });
  }

  // If streaming is requested, return SSE
  if (stream) {
    return handleSSE(request, userId);
  }

  // Otherwise, return single status (your existing logic)
  return getSingleStatus(userId);
}

async function handleSSE(request: NextRequest, userId: string) {
  const stream = new TransformStream();
  const writer = stream.writable.getWriter();
  const encoder = new TextEncoder();

  const sendEvent = async (data: any) => {
    try {
      await writer.write(encoder.encode(`data: ${JSON.stringify(data)}\n\n`));
    } catch (error) {
      console.error('Error writing to stream:', error);
    }
  };

  // Send heartbeat to keep connection alive
  const sendHeartbeat = () => {
    sendEvent({ type: 'heartbeat', timestamp: Date.now() });
  };

  // Fetch and send Spotify status
  const fetchAndSendStatus = async () => {
    try {
      const status = await fetchSpotifyStatus(userId);
      await sendEvent({ 
        type: 'status', 
        data: status,
        timestamp: Date.now() 
      });
    } catch (error) {
      console.error('Error fetching Spotify status:', error);
      await sendEvent({ 
        type: 'error', 
        error: 'Failed to fetch status',
        timestamp: Date.now() 
      });
    }
  };

  // Send initial status
  await fetchAndSendStatus();

  // Set up intervals
  const statusInterval = setInterval(fetchAndSendStatus, 10000); // Every 10 seconds
  const heartbeatInterval = setInterval(sendHeartbeat, 30000); // Every 30 seconds

  // Clean up on client disconnect
  request.signal.onabort = () => {
    clearInterval(statusInterval);
    clearInterval(heartbeatInterval);
    writer.close().catch(console.error);
  };

  return new Response(stream.readable, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Cache-Control',
    },
  });
}

async function getSingleStatus(userId: string) {
  // Your existing single status logic
  const status = await fetchSpotifyStatus(userId);
  return NextResponse.json(status);
}

async function fetchSpotifyStatus(userId: string) {
  // Validate environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE;
  const spotifyClientId = process.env.SPOTIFY_CLIENT_ID;
  const spotifyClientSecret = process.env.SPOTIFY_CLIENT_SECRET;

  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase configuration');
  }

  if (!spotifyClientId || !spotifyClientSecret) {
    throw new Error('Missing Spotify configuration');
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  // Check if user has Spotify account linked
  const { data: spotifyAccount, error } = await supabase
    .from('spotify_accounts')
    .select('*')
    .eq('user_id', userId)
    .single();

  if (error || !spotifyAccount) {
    return {
      linked: false,
      loginUrl: `/api/spotify/login?userId=${userId}`
    };
  }

  // Check if token is valid and refresh if needed
  const now = new Date();
  let accessToken = spotifyAccount.access_token;

  if (new Date(spotifyAccount.expires_at) <= now) {      
    // Token expired, refresh it
    try {
      const tokenResponse = await axios.post(
        'https://accounts.spotify.com/api/token',
        new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token: spotifyAccount.refresh_token,
          client_id: spotifyClientId,
          client_secret: spotifyClientSecret,
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      accessToken = tokenResponse.data.access_token;
      const expiresIn = tokenResponse.data.expires_in;
      const newExpiresAt = new Date();
      newExpiresAt.setSeconds(newExpiresAt.getSeconds() + expiresIn);

      // Update tokens in database
      await supabase
        .from('spotify_accounts')
        .update({
          access_token: accessToken,
          expires_at: newExpiresAt.toISOString(),
        })
        .eq('user_id', userId);

    } catch (refreshError) {
      throw new Error('Token refresh failed');
    }
  }

  // Get current playing track
  try {
    const currentlyPlayingResponse = await axios.get(
      'https://api.spotify.com/v1/me/player/currently-playing',
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    if (currentlyPlayingResponse.status === 204) {
      return {
        linked: true,
        isPlaying: false,
        currentlyPlaying: null
      };
    }

    const data = currentlyPlayingResponse.data;
    const track = data.item;

    if (!track) {
      return {
        linked: true,
        isPlaying: false,
        currentlyPlaying: null
      };
    }

    const artists = track.artists?.map((a: any) => a.name).join(', ') || 'Unknown Artist';
    const currentlyPlaying = `${track.name} - ${artists}`;

    // Extract album artwork
    const albumImages = track.album?.images || [];
    const albumArtwork = {
      large: albumImages.find((img: any) => img.height === 640)?.url || albumImages[0]?.url,
      medium: albumImages.find((img: any) => img.height === 300)?.url || albumImages[1]?.url,
      small: albumImages.find((img: any) => img.height === 64)?.url || albumImages[2]?.url,
      default: albumImages[0]?.url || null
    };

    return {
      linked: true,
      isPlaying: data.is_playing || false,
      currentlyPlaying,
      track: {
        name: track.name || 'Unknown Track',
        artist: artists,
        album: track.album?.name || 'Unknown Album',
        duration_ms: track.duration_ms,
        progress_ms: data.progress_ms,
        albumArtwork,
        albumImage: albumImages[0]?.url || null,
      }
    };

  } catch (spotifyError: any) {
    if (spotifyError?.response?.status === 401) {
      return {
        linked: false,
        error: 'Spotify authentication expired',
        loginUrl: `/api/spotify/login?userId=${userId}`
      };
    }

    return {
      linked: true,
      isPlaying: false,
      currentlyPlaying: null,
      error: 'Could not fetch current track'
    };
  }
}
