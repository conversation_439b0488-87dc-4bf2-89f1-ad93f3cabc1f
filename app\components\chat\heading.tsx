import React from 'react'
import { useUser } from "@/lib/user-store/provider"
import { TextAnimate } from "@/components/magicui/text-animate" // Adjust path if needed

export default function Heading() {
  const { user } = useUser()

  // Safely extract first name or fallback to 'Guest'
  const firstName = user?.display_name?.split(' ')[0] ?? 'Guest'

  // Construct heading text safely
  const headingText = `What's on your mind, ${firstName}?`

  // Ensure headingText is always a string before passing to TextAnimate
  const safeText = typeof headingText === 'string' ? headingText : 'What\'s on your mind, Guest?'

  return (
    <TextAnimate animation="blurIn" once className='mb-6 text-3xl font-medium tracking-tight text-center'>
      {safeText}
    </TextAnimate>
  )
}
