import { NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/client';
import { isSupabaseEnabled } from '@/lib/supabase/config';

export async function POST(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get('userId');

    if (!userId) {
      return new Response(JSON.stringify({ error: 'User ID required' }), { 
        status: 400 
      });
    }

    if (!isSupabaseEnabled) {
      return new Response(JSON.stringify({ 
        error: 'Database not configured' 
      }), { status: 500 });
    }

    const supabase = createClient();
    if (!supabase) {
      return new Response(JSON.stringify({ 
        error: 'Database client not available' 
      }), { status: 500 });
    }

    // Delete Notion auth from database
    const { error } = await supabase
      .from('notion_auth')
      .delete()
      .eq('user_id', userId);

    if (error) {
      console.error('Database error:', error);
      return new Response(JSON.stringify({ 
        error: 'Failed to unlink Notion account' 
      }), { status: 500 });
    }

    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Notion account unlinked successfully' 
    }), { status: 200 });

  } catch (error) {
    console.error('Notion unlink error:', error);
    return new Response(JSON.stringify({ 
      error: 'Failed to unlink Notion account' 
    }), { status: 500 });
  }
}
