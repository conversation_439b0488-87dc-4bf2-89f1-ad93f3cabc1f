import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE!
)

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber, otp, userId } = await request.json()

    if (!phoneNumber || !otp || !userId) {
      return NextResponse.json({ error: 'Phone number, OTP, and user ID required' }, { status: 400 })
    }

    // Get the stored OTP from Supabase
    const { data, error } = await supabase
      .from('phone_verifications')
      .select('*')
      .eq('user_id', userId)
      .eq('phone_number', phoneNumber)
      .eq('verified', false)
      .single()

    if (error || !data) {
      return NextResponse.json({ error: 'No pending verification found' }, { status: 400 })
    }

    // Check if <PERSON><PERSON> has expired
    const expiresAt = new Date(data.expires_at)
    const now = new Date()
    
    if (now > expiresAt) {
      return NextResponse.json({ error: 'OTP has expired. Please request a new one.' }, { status: 400 })
    }

    // Verify OTP
    if (data.otp_code !== otp) {
      return NextResponse.json({ error: 'Invalid OTP. Please try again.' }, { status: 400 })
    }

    // Mark as verified
    const { error: updateError } = await supabase
      .from('phone_verifications')
      .update({
        verified: true,
        verified_at: new Date().toISOString()
      })
      .eq('id', data.id)

    if (updateError) {
      console.error('Update error:', updateError)
      return NextResponse.json({ error: 'Failed to verify phone number' }, { status: 500 })
    }

    return NextResponse.json({ 
      message: 'Phone number verified successfully',
      success: true 
    })

  } catch (error) {
    console.error('Verify OTP error:', error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Failed to verify OTP' 
    }, { status: 500 })
  }
}
