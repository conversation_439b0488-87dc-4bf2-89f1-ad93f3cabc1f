import { openproviders } from "@/lib/openproviders"
import { ModelConfig } from "../types"

const geminiModels: ModelConfig[] = [
  {
    id: "gemini-2.0-flash",
    name: "Human",
    provider: "Google",
    providerId: "google",
    modelFamily: "Gemini",
    baseProviderId: "google",
    apiSdk: (apiKey?: string) =>
      openproviders("gemini-2.0-flash", undefined, apiKey),
  },
  {
    id: "gemini-2.5-flash-lite",
    name: "assistant",
    provider: "Google",
    providerId: "google",
    modelFamily: "Gemini",
    baseProviderId: "google",
    apiSdk: (apiKey?: string) =>
      openproviders("gemini-2.5-flash-lite", undefined, apiKey),
  },
  {
    id: "gemini-2.5-flash-preview-05-20",
    name: "Study AI",
    provider: "Google",
    providerId: "google",
    modelFamily: "Gemini",
    baseProviderId: "google",
    apiSdk: (apiKey?: string) =>
      openproviders("gemini-2.5-flash-preview-05-20", undefined, apiKey),
  }
]

export { geminiModels }
