import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE!
)

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 })
    }

    // Delete all phone verifications for this user
    const { error } = await supabase
      .from('phone_verifications')
      .delete()
      .eq('user_id', userId)

    if (error) {
      console.error('Delete error:', error)
      return NextResponse.json({ error: 'Failed to unlink phone number' }, { status: 500 })
    }

    return NextResponse.json({ 
      message: 'Phone number unlinked successfully',
      success: true 
    })

  } catch (error) {
    console.error('Unlink phone error:', error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Failed to unlink phone' 
    }, { status: 500 })
  }
}
