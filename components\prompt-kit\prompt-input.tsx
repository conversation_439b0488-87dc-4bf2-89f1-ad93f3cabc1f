"use client"

import { Textarea } from "@/components/ui/textarea"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react"

type PromptInputContextType = {
  isLoading: boolean
  value: string
  setValue: (value: string) => void
  maxHeight: number | string
  onSubmit?: () => void
  disabled?: boolean
  currentPlaceholder: string
}

const PromptInputContext = createContext<PromptInputContextType>({
  isLoading: false,
  value: "",
  setValue: () => { },
  maxHeight: 240,
  onSubmit: undefined,
  disabled: false,
  currentPlaceholder: "",
})

function usePromptInput() {
  const context = useContext(PromptInputContext)
  if (!context) {
    throw new Error("usePromptInput must be used within a PromptInput")
  }
  return context
}

type PromptInputProps = {
  isLoading?: boolean
  value?: string
  onValueChange?: (value: string) => void
  maxHeight?: number | string
  onSubmit?: () => void
  children: React.ReactNode
  className?: string
  placeholderTexts?: string[]
  placeholderDelay?: number
}

function PromptInput({
  className,
  isLoading = false,
  maxHeight = 240,
  value,
  onValueChange,
  onSubmit,
  children,
  placeholderTexts = [
    "Explain quantum physics like I'm 10",
    "Explain stoicism in simple terms",
    "Explain how a neural network works",
    "Explain the difference between AI and AGI",
    "Study tips for effective exam preparation",
    "Explain the Pythagorean theorem with examples",
    "Create flashcards for world capitals",
    "Summarize NCERT Class 10 History Chapter 1",
    "Explain photosynthesis simply",
    "Inspire me with a beautiful quote about creativity",
    "Inspire me with a writing prompt about solitude",
    "Inspire me with a poetic way to start a newsletter",
    "Inspire me by describing a peaceful morning in nature",
    "Research the pros and cons of remote work",
    "Research the differences between Apple Vision Pro and Meta Quest",
    "Research best practices for password security",
    "Research the latest trends in renewable energy",
    "Design a color palette for a tech blog",
    "Design a UX checklist for mobile apps",
    "Design 5 great font pairings for a landing page",
    "Design better CTAs with useful tips",
    "Summarize the French Revolution",
    "Summarize the plot of Inception",
    "Summarize World War II in 5 sentences",
    "Summarize the benefits of meditation",
  ],
  placeholderDelay = 3000,
}: PromptInputProps) {
  const [internalValue, setInternalValue] = useState(value || "")
  const [currentPlaceholderIndex, setCurrentPlaceholderIndex] = useState(0)
  const [currentPlaceholder, setCurrentPlaceholder] = useState(placeholderTexts[0])

  const handleChange = (newValue: string) => {
    setInternalValue(newValue)
    onValueChange?.(newValue)
  }

  // Rotate placeholder text
  useEffect(() => {
    if (placeholderTexts.length <= 1) return

    const interval = setInterval(() => {
      setCurrentPlaceholderIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % placeholderTexts.length
        setCurrentPlaceholder(placeholderTexts[nextIndex])
        return nextIndex
      })
    }, placeholderDelay)

    return () => clearInterval(interval)
  }, [placeholderTexts, placeholderDelay])

  return (
    <PromptInputContext.Provider
      value={{
        isLoading,
        value: value ?? internalValue,
        setValue: onValueChange ?? handleChange,
        maxHeight,
        onSubmit,
        disabled: isLoading,
        currentPlaceholder,
      }}
    >
      <div
        className={cn(
          "border-input bg-background rounded-3xl border p-2 shadow-xs transition-all duration-300",
          className
        )}
      >
        {children}
      </div>
    </PromptInputContext.Provider>
  )
}

export type PromptInputTextareaProps = {
  disableAutosize?: boolean
  enableTypingAnimation?: boolean
} & Omit<React.ComponentProps<typeof Textarea>, 'placeholder'>

function PromptInputTextarea({
  className,
  onKeyDown,
  disableAutosize = false,
  enableTypingAnimation = false,
  ...props
}: PromptInputTextareaProps) {
  const { value, setValue, maxHeight, onSubmit, disabled, currentPlaceholder } = usePromptInput()
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [typingPlaceholder, setTypingPlaceholder] = useState("")
  const [isTyping, setIsTyping] = useState(false)

  // Typing animation effect
  useEffect(() => {
    if (!enableTypingAnimation) {
      setTypingPlaceholder(currentPlaceholder)
      return
    }

    setIsTyping(true)
    setTypingPlaceholder("")

    let currentIndex = 0
    const typingInterval = setInterval(() => {
      if (currentIndex < currentPlaceholder.length) {
        setTypingPlaceholder(currentPlaceholder.slice(0, currentIndex + 1))
        currentIndex++
      } else {
        setIsTyping(false)
        clearInterval(typingInterval)
      }
    }, 50) // Adjust typing speed here

    return () => clearInterval(typingInterval)
  }, [currentPlaceholder, enableTypingAnimation])

  useEffect(() => {
    if (disableAutosize || !textareaRef.current) return

    // Reset height to auto first to properly measure scrollHeight
    textareaRef.current.style.height = "auto"

    // Set the height based on content
    textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
  }, [value, disableAutosize])

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      onSubmit?.()
    }
    onKeyDown?.(e)
  }

  const maxHeightStyle =
    typeof maxHeight === "number" ? `${maxHeight}px` : maxHeight

  return (
    <Textarea
      ref={textareaRef}
      autoFocus
      value={value}
      onChange={(e) => setValue(e.target.value)}
      onKeyDown={handleKeyDown}
      placeholder={enableTypingAnimation ? typingPlaceholder + (isTyping ? "|" : "") : currentPlaceholder}
      className={cn(
        "text-primary min-h-[44px] w-full resize-none border-none bg-transparent shadow-none outline-none focus-visible:ring-0 focus-visible:ring-offset-0",
        "overflow-y-auto",
        "placeholder:transition-opacity placeholder:duration-300",
        className
      )}
      style={{
        maxHeight: maxHeightStyle,
      }}
      rows={1}
      disabled={disabled}
      {...props}
    />
  )
}

type PromptInputActionsProps = React.HTMLAttributes<HTMLDivElement>

function PromptInputActions({
  children,
  className,
  ...props
}: PromptInputActionsProps) {
  return (
    <div className={cn("flex items-center gap-2", className)} {...props}>
      {children}
    </div>
  )
}

type PromptInputActionProps = {
  className?: string
  tooltip: React.ReactNode
  children: React.ReactNode
  side?: "top" | "bottom" | "left" | "right"
} & React.ComponentProps<typeof Tooltip>

function PromptInputAction({
  tooltip,
  children,
  className,
  side = "top",
  ...props
}: PromptInputActionProps) {
  const { disabled } = usePromptInput()

  return (
    <Tooltip {...props}>
      <TooltipTrigger asChild disabled={disabled}>
        {children}
      </TooltipTrigger>
      <TooltipContent side={side} className={className}>
        {tooltip}
      </TooltipContent>
    </Tooltip>
  )
}

export {
  PromptInput,
  PromptInputTextarea,
  PromptInputActions,
  PromptInputAction,
}
