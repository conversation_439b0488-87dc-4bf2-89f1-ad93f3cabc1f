// lib/image-upload.ts
export interface ImageUploadResponse {
  success: boolean;
  filename: string;
  url: string;
}

export async function uploadImage(file: File): Promise<ImageUploadResponse> {
  const formData = new FormData();
  formData.append('image', file);

  const response = await fetch('http://103.86.176.140:57330/upload', {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`Upload failed: ${response.statusText}`);
  }

  return response.json();
}

export async function uploadImages(files: File[]): Promise<ImageUploadResponse[]> {
  const uploadPromises = files.map(file => uploadImage(file));
  return Promise.all(uploadPromises);
}
