"use client"

import { useState } from "react"
import { AnimatePresence, motion } from "motion/react"
import { CaretDown, Link } from "@phosphor-icons/react"
import { cn } from "@/lib/utils"
import { formatUrl, addUTM, getFavicon } from "./utils"
import type { SourceUIPart } from "@phosphor-icons/react"

// === SourcesList Component ===
type SourcesListProps = {
  sources: { id: string; url: string; title: string }[]
  className?: string
}

const TRANSITION = {
  type: "spring",
  duration: 0.2,
  bounce: 0,
}

export function SourcesList({ sources, className }: SourcesListProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [failedFavicons, setFailedFavicons] = useState<Set<string>>(new Set())

  const handleFaviconError = (url: string) => {
    setFailedFavicons((prev) => new Set(prev).add(url))
  }

  return (
    <div className={cn("my-4", className)}>
      <div className="border-border flex flex-col gap-0 overflow-hidden rounded-md border">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          type="button"
          className="hover:bg-accent flex w-full flex-row items-center rounded-t-md px-3 py-2 transition-colors"
        >
          <div className="flex flex-row items-center gap-2 text-left text-sm flex-1">
            Sources
            <div className="flex -space-x-1">
              {sources.slice(0, 3).map((source, idx) => {
                const faviconUrl = getFavicon(source.url)
                const showFallback = !faviconUrl || failedFavicons.has(source.url)

                return showFallback ? (
                  <div
                    key={`${source.url}-${idx}`}
                    className="bg-muted border-background h-4 w-4 rounded-full"
                  />
                ) : (
                  <img
                    key={`${source.url}-${idx}`}
                    src={faviconUrl}
                    alt={`Favicon for ${source.title}`}
                    width={16}
                    height={16}
                    className="border-background rounded-sm"
                    onError={() => handleFaviconError(source.url)}
                  />
                )
              })}
              {sources.length > 3 && (
                <span className="text-muted ml-1 text-xs">+{sources.length - 3}</span>
              )}
            </div>
          </div>
          <CaretDown className={cn("size-4 transition-transform", isExpanded && "rotate-180")} />
        </button>

        <AnimatePresence initial={false}>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={TRANSITION}
              className="overflow-hidden"
            >
              <ul className="space-y-2 px-3 py-3">
                {sources.map((source) => {
                  const faviconUrl = getFavicon(source.url)
                  const showFallback = !faviconUrl || failedFavicons.has(source.url)

                  return (
                    <li key={source.id} className="flex items-center text-sm">
                      <div className="min-w-0 flex-1">
                        <a
                          href={addUTM(source.url)}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-1 text-primary group line-clamp-1 hover:underline"
                        >
                          {showFallback ? (
                            <div className="bg-muted h-4 w-4 rounded-full shrink-0" />
                          ) : (
                            <img
                              src={faviconUrl}
                              alt={`Favicon for ${source.title}`}
                              width={16}
                              height={16}
                              className="shrink-0 rounded-sm"
                              onError={() => handleFaviconError(source.url)}
                            />
                          )}
                          <span>{source.title}</span>
                          <Link className="opacity-70 group-hover:opacity-100 size-3" />
                        </a>
                        <div className="text-muted text-xs truncate">{formatUrl(source.url)}</div>
                      </div>
                    </li>
                  )
                })}
              </ul>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

// === Utility to convert your API results to Sources format ===
export function formatSourcesFromResults(results: any[]): { id: string; url: string; title: string }[] {
  const sources: { id: string; url: string; title: string }[] = []

  results.forEach((item, idx) => {
    const crawled = item.crawledContent
    if (!crawled) return

    // Prefer first link from links array as source URL
    const baseUrl = crawled.links && crawled.links.length > 0 ? crawled.links[0].href : null
    if (baseUrl) {
      sources.push({
        id: baseUrl + "-" + idx,
        url: baseUrl,
        title: crawled.fullTitle || crawled.links[0].text || baseUrl,
      })
    }
  })

  return sources
}