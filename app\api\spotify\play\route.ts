import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  const { userId, action, trackUri } = await request.json();

  if (!userId || !action) {
    return NextResponse.json({ error: 'Missing userId or action' }, { status: 400 });
  }

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE;
  const spotifyClientId = process.env.SPOTIFY_CLIENT_ID;
  const spotifyClientSecret = process.env.SPOTIFY_CLIENT_SECRET;

  if (!supabaseUrl || !supabaseKey || !spotifyClientId || !spotifyClientSecret) {
    return NextResponse.json({ 
      error: 'Configuration error',
      success: false
    }, { status: 500 });
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // Get Spotify account
    const { data: spotifyAccount, error } = await supabase
      .from('spotify_accounts')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error || !spotifyAccount) {
      return NextResponse.json({
        success: false,
        error: 'Spotify account not linked'
      });
    }

    // Check and refresh token if needed
    let accessToken = spotifyAccount.access_token;
    const now = new Date();

    if (new Date(spotifyAccount.expires_at) <= now) {
      try {
        const tokenResponse = await axios.post(
          'https://accounts.spotify.com/api/token',
          new URLSearchParams({
            grant_type: 'refresh_token',
            refresh_token: spotifyAccount.refresh_token,
            client_id: spotifyClientId,
            client_secret: spotifyClientSecret,
          }),
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          }
        );

        accessToken = tokenResponse.data.access_token;
        const expiresIn = tokenResponse.data.expires_in;
        const newExpiresAt = new Date();
        newExpiresAt.setSeconds(newExpiresAt.getSeconds() + expiresIn);

        await supabase
          .from('spotify_accounts')
          .update({
            access_token: accessToken,
            expires_at: newExpiresAt.toISOString(),
          })
          .eq('user_id', userId);

      } catch (refreshError) {
        return NextResponse.json({
          success: false,
          error: 'Token refresh failed'
        });
      }
    }

    // Perform the action
    let endpoint = '';
    let method = 'PUT';
    let body = {};

    switch (action) {
      case 'play':
        endpoint = '/me/player/play';
        if (trackUri) {
          body = { uris: [trackUri] };
        }
        break;
      case 'pause':
        endpoint = '/me/player/pause';
        break;
      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action'
        });
    }

    await axios({
      method,
      url: `https://api.spotify.com/v1${endpoint}`,
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      data: Object.keys(body).length > 0 ? body : undefined,
    });

    return NextResponse.json({
      success: true,
      message: action === 'play' ? 'Music started/resumed' : 'Music paused'
    });

  } catch (error: any) {
    console.error('Spotify play/pause error:', error);
    return NextResponse.json({
      success: false,
      error: error?.response?.data?.error?.message || 'Failed to control playback'
    });
  }
}
