import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE!
)

export async function POST(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const userId = searchParams.get('userId')

  if (!userId) {
    return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
  }

  try {
    // Get the integration to revoke the token
    const { data: discordIntegration } = await supabase
      .from('discord_integrations')
      .select('access_token')
      .eq('user_id', userId)
      .single()

    if (discordIntegration) {
      // Revoke the access token with Discord
      try {
        await fetch('https://discord.com/api/oauth2/token/revoke', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            client_id: process.env.DISCORD_CLIENT_ID!,
            client_secret: process.env.DISCORD_CLIENT_SECRET!,
            token: discordIntegration.access_token,
          }),
        })
      } catch (error) {
        console.error('Error revoking Discord token:', error)
        // Continue with deletion even if revocation fails
      }

      // Delete the integration from Supabase
      const { error: deleteError } = await supabase
        .from('discord_integrations')
        .delete()
        .eq('user_id', userId)

      if (deleteError) {
        throw new Error('Failed to delete Discord integration')
      }
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error unlinking Discord:', error)
    return NextResponse.json(
      { error: 'Failed to unlink Discord account' },
      { status: 500 }
    )
  }
}
