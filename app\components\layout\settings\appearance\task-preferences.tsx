"use client"

import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Card } from "@/components/ui/card"
import { X } from "lucide-react"
import { createBrowserClient } from "@supabase/ssr" // Use browser client for client components
import { useUser } from "@/lib/user-store/provider"

// Update Task type to match your Supabase table schema
type Task = {
  id: number
  text: string
  done: boolean
  user_id: string // Adjust type based on your schema (e.g., string for UUID)
}

// Create the Supabase client once (browser client is synchronous and safe for client-side)
const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export function TaskPreferences() {
  const [tasks, setTasks] = useState<Task[]>([])
  const { user } = useUser()

  // Derive userId from the user object (assuming user has an 'id' property)
  const userId = user?.id // Adjust based on your user object's structure

  const fetchTasks = useCallback(async () => {
    if (!userId) return // Prevent fetch if no userId

    const { data, error } = await supabase
      .from("tasks")
      .select("*")
      .eq("user_id", userId) // Filter by user_id if tasks are user-specific

    if (error) {
      console.error("Error fetching tasks:", error)
      return
    }

    setTasks((data as Task[]) || [])
  }, [userId])

  useEffect(() => {
    fetchTasks()
  }, [fetchTasks])

  const toggleDone = async (id: number) => {
    const taskToUpdate = tasks.find((task) => task.id === id)
    if (!taskToUpdate || !userId) return

    const { error } = await supabase
      .from("tasks")
      .update({ done: !taskToUpdate.done })
      .eq("id", id)
      .eq("user_id", userId) // Ensure only user's task is updated

    if (error) {
      console.error("Error updating task:", error)
      return
    }

    // Refresh local state
    fetchTasks()
  }

  const deleteTask = async (id: number) => {
    if (!userId) return

    const { error } = await supabase
      .from("tasks")
      .delete()
      .eq("id", id)
      .eq("user_id", userId) // Ensure only user's task is deleted

    if (error) {
      console.error("Error deleting task:", error)
      return
    }

    // Refresh local state
    fetchTasks()
  }

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Your Task List</h3>

      <div className="overflow-y-auto space-y-2 pr-1">
        {tasks.length === 0 && (
          <Card className="p-4 text-center text-muted-foreground">
            No tasks available.
          </Card>
        )}
        {tasks.map(({ id, text, done }) => (
          <Card
            key={id}
            className={`flex items-center justify-between flex-row shadow-none p-2 px-3 rounded-lg transition
      ${done ? "bg-green-100 hover:bg-green-200" : "hover:bg-muted"}`}
          >
            <label className="flex items-center gap-3 cursor-pointer select-none">
              <Checkbox checked={done} onCheckedChange={() => toggleDone(id)} />
              <span className={done ? "line-through text-green-800 font-semibold" : "text-foreground"}>
                {text}
              </span>
            </label>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => deleteTask(id)}
              aria-label="Delete task"
              className="text-red-600 hover:text-red-800 focus-visible:ring-red-600"
            >
              <X />
            </Button>
          </Card>
        ))}

      </div>
    </div>
  )
}
