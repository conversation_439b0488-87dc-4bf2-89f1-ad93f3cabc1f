import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import mysql from 'mysql2/promise'

// Try different import methods - use the one that works in your environment
// Option 1: Named import (your current approach)
import { Expo } from 'expo-server-sdk'

// Option 2: Default import (uncomment if Option 1 fails)
// import Expo from 'expo-server-sdk'

// Option 3: CommonJS require (uncomment if Options 1 & 2 fail)
// const { Expo } = require('expo-server-sdk')

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE!
)

// MySQL connection config
const dbConfig = {
  host: process.env.MYSQL_HOST,
  user: process.env.MYSQL_USER,
  password: process.env.MYSQL_PASSWORD,
  database: process.env.MYSQL_DATABASE,
  port: parseInt(process.env.MYSQL_PORT || '3306')
}

// Create Expo instance outside the function
let expo: Expo | null = null

// Initialize Expo instance with error handling
try {
  expo = new Expo()
} catch (error) {
  console.error('Failed to initialize Expo:', error)
}

// Manual validation function as fallback
function isValidExpoPushToken(token: string): boolean {
  if (typeof token !== 'string') return false
  return token.startsWith('ExponentPushToken[') || 
         token.startsWith('ExpoPushToken[') ||
         token.match(/^Expo[A-Za-z0-9_-]+$/) !== null
}

export async function POST(request: NextRequest) {
  let connection: mysql.Connection | null = null
  
  try {
    const { phoneNumber, userId } = await request.json()

    if (!phoneNumber || !userId) {
      return NextResponse.json({ error: 'Phone number and user ID required' }, { status: 400 })
    }

    // Validate phone number format (basic validation)
    const phoneRegex = /^\+?[1-9]\d{1,14}$/
    if (!phoneRegex.test(phoneNumber.replace(/\s+/g, ''))) {
      return NextResponse.json({ error: 'Invalid phone number format' }, { status: 400 })
    }

    // Generate 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString()
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes

    // Store OTP in Supabase
    const { error: supabaseError } = await supabase
      .from('phone_verifications')
      .upsert({
        user_id: userId,
        phone_number: phoneNumber,
        otp_code: otp,
        expires_at: expiresAt.toISOString(),
        verified: false,
        created_at: new Date().toISOString()
      }, {
        onConflict: 'user_id,phone_number'
      })

    if (supabaseError) {
      console.error('Supabase error:', supabaseError)
      return NextResponse.json({ error: 'Failed to store OTP' }, { status: 500 })
    }

    // Get user's Expo push token from MySQL
    connection = await mysql.createConnection(dbConfig)
    
    const [rows] = await connection.execute(
      'SELECT pushtoken FROM users WHERE email = ? LIMIT 1',
      [phoneNumber] // Phone number stored in email column as per your requirement
    )

    const user = (rows as any[])[0]
    
    // Debug logging
    console.log('Expo object available:', !!Expo)
    console.log('Expo.isExpoPushToken available:', typeof Expo?.isExpoPushToken)
    console.log('User push token:', user?.pushtoken)
    
    if (user?.pushtoken) {
      let isValidToken = false
      
      // Try using Expo.isExpoPushToken if available
      if (Expo && typeof Expo.isExpoPushToken === 'function') {
        try {
          isValidToken = Expo.isExpoPushToken(user.pushtoken)
          console.log('Expo validation result:', isValidToken)
        } catch (error) {
          console.error('Error validating token with Expo:', error)
          // Fallback to manual validation
          isValidToken = isValidExpoPushToken(user.pushtoken)
          console.log('Manual validation result:', isValidToken)
        }
      } else {
        // Use manual validation if Expo method is not available
        console.log('Using manual token validation')
        isValidToken = isValidExpoPushToken(user.pushtoken)
        console.log('Manual validation result:', isValidToken)
      }
      
      if (isValidToken && expo) {
        // Send push notification with OTP
        const messages = [{
          to: user.pushtoken,
          sound: 'default' as const,
          title: 'Phone Verification',
          body: `Your verification code is: ${otp}`,
          data: { 
            type: 'otp_verification',
            phoneNumber,
            otp 
          },
        }]

        try {
          const chunks = expo.chunkPushNotifications(messages)
          const tickets = []
          
          for (let chunk of chunks) {
            const ticketChunk = await expo.sendPushNotificationsAsync(chunk)
            tickets.push(...ticketChunk)
          }

          // Check for any receipt errors (optional but recommended)
          const receiptIds = tickets
            .filter(ticket => ticket.status === 'ok')
            .map(ticket => ticket.id)

          if (receiptIds.length > 0) {
            console.log('Push notification sent successfully')
          } else {
            console.log('Push notification failed - no successful tickets')
          }

        } catch (pushError) {
          console.error('Push notification error:', pushError)
          // Don't fail the request if push notification fails
        }
      } else {
        if (!expo) {
          console.log('Expo instance not available')
        } else {
          console.log('Invalid or no Expo push token found for user:', user.pushtoken)
        }
      }
    } else {
      console.log('No push token found for user')
    }

    return NextResponse.json({ 
      message: 'OTP sent successfully',
      success: true 
    })

  } catch (error) {
    console.error('Send OTP error:', error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Failed to send OTP' 
    }, { status: 500 })
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}
