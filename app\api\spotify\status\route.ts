// /app/api/spotify/status/route.ts
import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const userId = searchParams.get('userId');

  if (!userId) {
    return NextResponse.json({ error: 'Missing userId' }, { status: 400 });
  }

  // Validate environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE;
  const spotifyClientId = process.env.SPOTIFY_CLIENT_ID;
  const spotifyClientSecret = process.env.SPOTIFY_CLIENT_SECRET;

  // Check for missing environment variables
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase configuration:', {
      url: !!supabaseUrl,
      key: !!supabaseKey
    });
    return NextResponse.json({ 
      error: 'Configuration error',
      linked: false,
      loginUrl: `/api/spotify/login?userId=${userId}`
    }, { status: 500 });
  }

  if (!spotifyClientId || !spotifyClientSecret) {
    console.error('Missing Spotify configuration:', {
      clientId: !!spotifyClientId,
      clientSecret: !!spotifyClientSecret
    });
    return NextResponse.json({ 
      error: 'Spotify configuration error',
      linked: false,
      loginUrl: `/api/spotify/login?userId=${userId}`
    }, { status: 500 });
  }

  // Initialize Supabase client with validated environment variables
  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // Check if user has Spotify account linked
    const { data: spotifyAccount, error } = await supabase
      .from('spotify_accounts')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error || !spotifyAccount) {
      return NextResponse.json({
        linked: false,
        loginUrl: `/api/spotify/login?userId=${userId}`
      });
    }

    // Check if token is valid and refresh if needed
    const now = new Date();
    let accessToken = spotifyAccount.access_token;

    if (new Date(spotifyAccount.expires_at) <= now) {      
      // Token expired, refresh it
      try {
        const tokenResponse = await axios.post(
          'https://accounts.spotify.com/api/token',
          new URLSearchParams({
            grant_type: 'refresh_token',
            refresh_token: spotifyAccount.refresh_token,
            client_id: spotifyClientId,
            client_secret: spotifyClientSecret,
          }),
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          }
        );

        accessToken = tokenResponse.data.access_token;
        const expiresIn = tokenResponse.data.expires_in;
        const newExpiresAt = new Date();
        newExpiresAt.setSeconds(newExpiresAt.getSeconds() + expiresIn);

        // Update tokens in database
        const { error: updateError } = await supabase
          .from('spotify_accounts')
          .update({
            access_token: accessToken,
            expires_at: newExpiresAt.toISOString(),
          })
          .eq('user_id', userId);

        if (updateError) {
          console.error('Failed to update refreshed token:', updateError);
        }

      } catch (refreshError: any) {
        console.error('Token refresh failed:', refreshError);
        console.error('Refresh error details:', refreshError?.response?.data);
        
        return NextResponse.json({
          linked: false,
          error: 'Token refresh failed',
          loginUrl: `/api/spotify/login?userId=${userId}`
        });
      }
    }

    // Get current playing track
    try {
      const currentlyPlayingResponse = await axios.get(
        'https://api.spotify.com/v1/me/player/currently-playing',
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      if (currentlyPlayingResponse.status === 204) {
        return NextResponse.json({
          linked: true,
          isPlaying: false,
          currentlyPlaying: null
        });
      }

      const data = currentlyPlayingResponse.data;
      const track = data.item;

      if (!track) {
        return NextResponse.json({
          linked: true,
          isPlaying: false,
          currentlyPlaying: null
        });
      }

      const artists = track.artists?.map((a: any) => a.name).join(', ') || 'Unknown Artist';
      const currentlyPlaying = `${track.name} - ${artists}`;

      // Extract album artwork - Spotify provides multiple sizes
      const albumImages = track.album?.images || [];
      const albumArtwork = {
        large: albumImages.find((img: any) => img.height === 640)?.url || albumImages[0]?.url,
        medium: albumImages.find((img: any) => img.height === 300)?.url || albumImages[1]?.url,
        small: albumImages.find((img: any) => img.height === 64)?.url || albumImages[2]?.url,
        // Fallback to the first available image if specific sizes aren't found
        default: albumImages[0]?.url || null
      };

      return NextResponse.json({
        linked: true,
        isPlaying: data.is_playing || false,
        currentlyPlaying,
        track: {
          name: track.name || 'Unknown Track',
          artist: artists,
          album: track.album?.name || 'Unknown Album',
          duration_ms: track.duration_ms,
          progress_ms: data.progress_ms,
          // Added album artwork/song pfp
          albumArtwork,
          // Alternative: if you want just one image URL
          albumImage: albumImages[0]?.url || null,
        }
      });

    } catch (spotifyError: any) {
      console.error('Spotify API error:', spotifyError);
      
      if (spotifyError?.response?.status === 401) {
        return NextResponse.json({
          linked: false,
          error: 'Spotify authentication expired',
          loginUrl: `/api/spotify/login?userId=${userId}`
        });
      }

      if (spotifyError?.response?.status === 429) {
        return NextResponse.json({
          linked: true,
          isPlaying: false,
          currentlyPlaying: null,
          error: 'Rate limit exceeded, try again later'
        });
      }

      return NextResponse.json({
        linked: true,
        isPlaying: false,
        currentlyPlaying: null,
        error: 'Could not fetch current track'
      });
    }

  } catch (error: any) {
    console.error('Spotify status error:', error);
    console.error('Error details:', error?.message);
    
    return NextResponse.json({
      linked: false,
      error: 'Internal server error',
      loginUrl: `/api/spotify/login?userId=${userId}`
    }, { status: 500 });
  }
}
