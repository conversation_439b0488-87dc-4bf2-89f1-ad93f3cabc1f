import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Function to create Supabase client with proper error handling
function createSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE
  
  console.log('Supabase URL exists:', !!supabaseUrl)
  console.log('Supabase Key exists:', !!supabaseKey)
  console.log('Supabase URL:', supabaseUrl)
  console.log('Supabase Key length:', supabaseKey?.length)
  
  if (!supabaseUrl) {
    throw new Error('NEXT_PUBLIC_SUPABASE_URL is required')
  }
  
  if (!supabaseKey) {
    throw new Error('SUPABASE_SERVICE_ROLE is required')
  }
  
  return createClient(supabaseUrl, supabaseKey)
}

// Helper function to safely escape HTML to prevent XSS
function escapeHtml(unsafe) {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;")
}

// Helper function to validate environment variables
function validateEnvironment() {
  const required = [
    'DISCORD_CLIENT_ID',
    'DISCORD_CLIENT_SECRET',
    'DISCORD_REDIRECT_URI',
    'NEXT_PUBLIC_SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE'
  ]
  
  const missing = required.filter(key => !process.env[key])
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }
}

export async function GET(request: NextRequest) {
  try {
    // Validate environment variables first
    validateEnvironment()
  } catch (envError) {
    console.error('Environment validation failed:', envError)
    return NextResponse.json(
      { error: 'Server configuration error', details: envError.message },
      { status: 500 }
    )
  }

  const { searchParams } = new URL(request.url)
  const code = searchParams.get('code')
  const state = searchParams.get('state') // This is our userId
  const error = searchParams.get('error')
  const errorDescription = searchParams.get('error_description')

  console.log('OAuth callback received:', { 
    code: !!code, 
    state: state ? `${state.substring(0, 8)}...` : null, // Log partial state for privacy
    error, 
    errorDescription 
  })

  // Handle OAuth errors with specific guidance
  if (error) {
    console.error('Discord OAuth error:', error, errorDescription)
    
    let errorMessage = escapeHtml(error)
    let specificGuidance = ''
    
    // Provide specific guidance for common errors
    if (error === 'invalid_scope') {
      errorMessage = 'Invalid Discord OAuth Scope'
      specificGuidance = `
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 8px; text-align: left;">
          <h3>🔧 How to Fix This:</h3>
          <p><strong>The Discord OAuth scope you're using is invalid or restricted.</strong></p>
          <p><strong>✅ Use only these valid scopes:</strong></p>
          <ul style="text-align: left; display: inline-block;">
            <li><code>identify</code> - Basic user info</li>
            <li><code>email</code> - User's email</li>
            <li><code>guilds</code> - User's Discord servers</li>
            <li><code>connections</code> - Connected accounts</li>
          </ul>
          <p><strong>❌ These scopes DON'T work:</strong></p>
          <ul style="text-align: left; display: inline-block;">
            <li><code>guilds.channels.read</code> (unreleased)</li>
            <li><code>guilds.members.read</code> (requires approval)</li>
            <li><code>dm_channels.messages.write</code> (restricted)</li>
          </ul>
          <p><strong>🔗 Working OAuth URL example:</strong></p>
          <code style="background: #f8f9fa; padding: 8px; display: block; word-break: break-all; margin: 10px 0; font-size: 12px;">
            https://discord.com/api/oauth2/authorize?client_id=YOUR_CLIENT_ID&amp;redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Fapi%2Fdiscord%2Fcallback&amp;response_type=code&amp;scope=identify%20email%20guilds&amp;state=USER_ID
          </code>
        </div>
      `
    } else if (error === 'access_denied') {
      errorMessage = 'Access Denied'
      specificGuidance = '<p style="color: #856404; background: #fff3cd; padding: 10px; border-radius: 6px;">You cancelled the Discord authorization. Please try again to connect your Discord account.</p>'
    } else if (error === 'invalid_request') {
      errorMessage = 'Invalid Request'
      specificGuidance = '<p style="color: #856404; background: #fff3cd; padding: 10px; border-radius: 6px;">There was an issue with the OAuth request. Please check your Discord application configuration.</p>'
    }
    
    const errorHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Discord Connection Failed</title>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              padding: 20px; 
              text-align: center; 
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              min-height: 100vh;
              margin: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              line-height: 1.6;
            }
            .container {
              max-width: 800px;
              margin: 0 auto;
              background: white;
              padding: 30px;
              border-radius: 16px;
              box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            }
            .error { 
              color: #dc3545; 
              margin: 20px 0; 
              font-size: 20px;
              font-weight: bold;
            }
            .description {
              color: #6c757d;
              margin: 10px 0;
              font-style: italic;
              background: #f8f9fa;
              padding: 10px;
              border-radius: 6px;
            }
            code {
              background: #f8f9fa;
              padding: 2px 6px;
              border-radius: 4px;
              font-family: 'SF Mono', Monaco, monospace;
            }
            ul {
              margin: 10px 0;
            }
            li {
              margin: 8px 0;
            }
            .retry-btn {
              background: #007bff;
              color: white;
              padding: 12px 24px;
              border: none;
              border-radius: 8px;
              cursor: pointer;
              font-size: 16px;
              margin: 15px 10px;
              text-decoration: none;
              display: inline-block;
              transition: all 0.2s ease;
            }
            .retry-btn:hover {
              background: #0056b3;
              transform: translateY(-1px);
            }
            .success-btn {
              background: #28a745;
            }
            .success-btn:hover {
              background: #1e7e34;
            }
            .icon {
              font-size: 48px;
              margin-bottom: 20px;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="icon">🔴</div>
            <h1>Discord Connection Failed</h1>
            <div class="error">${errorMessage}</div>
            ${errorDescription ? `<div class="description">${escapeHtml(errorDescription)}</div>` : ''}
            
            ${specificGuidance}
            
            <div style="margin: 30px 0;">
              <button class="retry-btn" onclick="window.close()">Close Window</button>
              <button class="retry-btn success-btn" onclick="retryConnection()">Try Again</button>
            </div>
            
            <div style="font-size: 12px; color: #6c757d; margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
              💡 <strong>Need help?</strong> If you continue having issues, check your Discord Developer Portal settings or contact support.
            </div>
          </div>
          
          <script>
            console.error('Discord OAuth error:', '${escapeHtml(error)}', '${escapeHtml(errorDescription || '')}');
            
            function retryConnection() {
              if (window.opener) {
                window.opener.postMessage({ 
                  type: 'DISCORD_AUTH_RETRY',
                  error: '${escapeHtml(error)}',
                  description: '${escapeHtml(errorDescription || 'Unknown error')}',
                  guidance: ${error === 'invalid_scope' ? "'Update your Discord OAuth scopes to use only: identify, email, guilds'" : "'Please try again'"}
                }, '*');
                window.close();
              } else {
                window.location.reload();
              }
            }
            
            // Send error message to parent window
            if (window.opener) {
              window.opener.postMessage({ 
                type: 'DISCORD_AUTH_ERROR', 
                error: '${escapeHtml(error)}',
                description: '${escapeHtml(errorDescription || 'Unknown error')}',
                guidance: ${error === 'invalid_scope' ? "'Check your Discord OAuth scopes - use only: identify, email, guilds'" : "'Please try again'"}
              }, '*');
            }
            
            // Auto close after 60 seconds if not manually closed
            setTimeout(() => {
              console.log('Auto-closing after 60 seconds');
              window.close();
            }, 60000);
          </script>
        </body>
      </html>
    `
    
    return new NextResponse(errorHtml, {
      headers: { 'Content-Type': 'text/html' },
    })
  }

  if (!code || !state) {
    console.error('Missing required parameters:', { code: !!code, state: !!state })
    
    const errorHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Discord Connection Failed</title>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              padding: 20px; 
              text-align: center; 
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              min-height: 100vh;
              margin: 0;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .container {
              max-width: 600px;
              margin: 0 auto;
              background: white;
              padding: 30px;
              border-radius: 16px;
              box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            }
            .error { color: #dc3545; margin: 20px 0; font-size: 18px; font-weight: bold; }
            .icon { font-size: 48px; margin-bottom: 20px; }
            .retry-btn {
              background: #007bff;
              color: white;
              padding: 12px 24px;
              border: none;
              border-radius: 8px;
              cursor: pointer;
              font-size: 16px;
              margin: 20px;
              transition: all 0.2s ease;
            }
            .retry-btn:hover {
              background: #0056b3;
              transform: translateY(-1px);
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="icon">🔴</div>
            <h1>Discord Connection Failed</h1>
            <div class="error">Missing Required Parameters</div>
            <p>The OAuth callback is missing required parameters (code or state).</p>
            <p>This usually means there was an issue with the Discord authorization flow.</p>
            <button onclick="window.close()" class="retry-btn">
              Close Window
            </button>
          </div>
          <script>
            if (window.opener) {
              window.opener.postMessage({ 
                type: 'DISCORD_AUTH_ERROR', 
                error: 'Missing required parameters',
                guidance: 'Please restart the Discord connection process'
              }, '*');
            }
            setTimeout(() => window.close(), 15000);
          </script>
        </body>
      </html>
    `
    
    return new NextResponse(errorHtml, {
      headers: { 'Content-Type': 'text/html' },
    })
  }

  try {
    // Create Supabase client with error handling
    let supabase
    try {
      supabase = createSupabaseClient()
    } catch (supabaseError) {
      console.error('Failed to create Supabase client:', supabaseError)
      throw new Error(`Supabase initialization failed: ${supabaseError.message}`)
    }

    console.log('Exchanging code for token...')
    
    // Exchange code for access token with timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout
    
    const tokenResponse = await fetch('https://discord.com/api/oauth2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'DiscordBot (YourApp, 1.0.0)',
      },
      body: new URLSearchParams({
        client_id: process.env.DISCORD_CLIENT_ID!,
        client_secret: process.env.DISCORD_CLIENT_SECRET!,
        grant_type: 'authorization_code',
        code,
        redirect_uri: process.env.DISCORD_REDIRECT_URI!,
      }),
      signal: controller.signal,
    })

    clearTimeout(timeoutId)

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.text()
      console.error('Token exchange failed:', tokenResponse.status, errorData)
      
      let parsedError
      try {
        parsedError = JSON.parse(errorData)
      } catch {
        parsedError = { error: 'token_exchange_failed', error_description: errorData }
      }
      
      throw new Error(`Token exchange failed: ${parsedError.error || tokenResponse.status} - ${parsedError.error_description || 'Unknown error'}`)
    }

    const tokens = await tokenResponse.json()
    
    // Validate token response
    if (!tokens.access_token) {
      throw new Error('No access token received from Discord')
    }
    
    console.log('Token exchange successful, expires in:', tokens.expires_in, 'seconds')

    // Get user info from Discord with timeout
    const userController = new AbortController()
    const userTimeoutId = setTimeout(() => userController.abort(), 8000) // 8 second timeout
    
    const userResponse = await fetch('https://discord.com/api/users/@me', {
      headers: {
        'Authorization': `Bearer ${tokens.access_token}`,
        'User-Agent': 'DiscordBot (YourApp, 1.0.0)',
      },
      signal: userController.signal,
    })

    clearTimeout(userTimeoutId)

    if (!userResponse.ok) {
      const errorData = await userResponse.text()
      console.error('User info fetch failed:', userResponse.status, errorData)
      throw new Error(`Failed to get user info from Discord: ${userResponse.status} - ${errorData}`)
    }

    const discordUser = await userResponse.json()
    
    // Validate user data
    if (!discordUser.id || !discordUser.username) {
      throw new Error('Invalid user data received from Discord')
    }
    
    console.log('Discord user info retrieved:', discordUser.username, `(ID: ${discordUser.id})`)

    // Save Discord integration to Supabase with better error handling
    const integrationData = {
      user_id: state,
      discord_id: discordUser.id,
      username: discordUser.username,
      discriminator: discordUser.discriminator || '0',
      avatar: discordUser.avatar,
      global_name: discordUser.global_name || discordUser.username,
      email: discordUser.email || null, // Only available if email scope was requested
      verified: discordUser.verified || false,
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token || null,
      token_type: tokens.token_type || 'Bearer',
      expires_at: new Date(Date.now() + (tokens.expires_in * 1000)).toISOString(),
      scope: tokens.scope || 'identify',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    console.log('Saving to Supabase...', { 
      user_id: `${state.substring(0, 8)}...`, 
      discord_id: discordUser.id,
      username: discordUser.username 
    })

    const { error: upsertError, data } = await supabase
      .from('discord_integrations')
      .upsert(integrationData, {
        onConflict: 'user_id'
      })
      .select()

    if (upsertError) {
      console.error('Supabase upsert error:', upsertError)
      throw new Error(`Failed to save Discord integration: ${upsertError.message}`)
    }

    console.log('Discord integration saved successfully')

    // Create enhanced success page with better security
    const displayName = escapeHtml(discordUser.global_name || discordUser.username)
    const username = escapeHtml(discordUser.username)
    const discriminator = escapeHtml(discordUser.discriminator || '0')
    const avatarUrl = discordUser.avatar 
      ? `https://cdn.discordapp.com/avatars/${discordUser.id}/${discordUser.avatar}.png?size=128`
      : `https://cdn.discordapp.com/embed/avatars/${parseInt(discordUser.id) % 5}.png`
    
    const successHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Discord Connected Successfully</title>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              padding: 20px; 
              text-align: center; 
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              min-height: 100vh;
              margin: 0;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .container {
              background: white;
              padding: 40px;
              border-radius: 20px;
              box-shadow: 0 10px 40px rgba(0,0,0,0.15);
              max-width: 500px;
              width: 90%;
              animation: slideIn 0.3s ease-out;
            }
            @keyframes slideIn {
              from { opacity: 0; transform: translateY(20px); }
              to { opacity: 1; transform: translateY(0); }
            }
            .success { 
              color: #28a745; 
              font-size: 24px; 
              margin: 20px 0; 
              font-weight: bold;
            }
            .user-info {
              background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
              padding: 25px;
              border-radius: 16px;
              margin: 25px 0;
              border-left: 4px solid #28a745;
              transition: transform 0.2s ease;
            }
            .user-info:hover {
              transform: translateY(-2px);
            }
            .avatar {
              width: 80px;
              height: 80px;
              border-radius: 50%;
              margin: 0 auto 15px;
              display: block;
              border: 4px solid #28a745;
              box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            }
            .countdown {
              color: #6c757d;
              font-size: 14px;
              margin-top: 20px;
              padding: 10px;
              background: #f8f9fa;
              border-radius: 8px;
            }
            .checkmark {
              font-size: 60px;
              color: #28a745;
              margin-bottom: 20px;
              animation: bounce 0.6s ease-out;
            }
            @keyframes bounce {
              0%, 20%, 60%, 100% { transform: translateY(0); }
              40% { transform: translateY(-10px); }
              80% { transform: translateY(-5px); }
            }
            .feature-list {
              text-align: left;
              background: #f8f9fa;
              padding: 15px;
              border-radius: 8px;
              margin: 20px 0;
            }
            .feature-list li {
              margin: 8px 0;
              color: #495057;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="checkmark">✅</div>
            <h1>Discord Connected Successfully!</h1>
            <div class="success">Your Discord account has been linked</div>
            
            <div class="user-info">
              <img src="${avatarUrl}" alt="Discord Avatar" class="avatar" onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">
              <div><strong>Display Name:</strong> ${displayName}</div>
              <div><strong>Username:</strong> ${username}${discriminator !== '0' ? `#${discriminator}` : ''}</div>
              <div><strong>Discord ID:</strong> ${escapeHtml(discordUser.id)}</div>
              ${discordUser.verified ? '<div style="color: #28a745;"><strong>✓ Verified Account</strong></div>' : ''}
            </div>
            
            <div class="feature-list">
              <h4>🎉 What's now available:</h4>
              <ul>
                <li>✅ Discord profile integration</li>
                <li>✅ Server/guild information access</li>
                <li>✅ Enhanced user experience</li>
              </ul>
            </div>
            
            <div class="countdown">
              This window will close automatically in <span id="countdown">5</span> seconds...
              <br><small>Click anywhere to close immediately</small>
            </div>
          </div>
          
          <script>
            console.log('Discord connection successful:', {
              username: '${username}',
              discriminator: '${discriminator}',
              id: '${escapeHtml(discordUser.id)}',
              global_name: '${escapeHtml(discordUser.global_name || '')}'
            });
            
            // Send success message to parent window
            if (window.opener) {
              window.opener.postMessage({ 
                type: 'DISCORD_AUTH_SUCCESS', 
                user: {
                  username: '${username}',
                  discriminator: '${discriminator}',
                  id: '${escapeHtml(discordUser.id)}',
                  avatar: '${escapeHtml(discordUser.avatar || '')}',
                  global_name: '${escapeHtml(discordUser.global_name || '')}',
                  display_name: '${displayName}',
                  verified: ${discordUser.verified || false}
                }
              }, '*');
            }
            
            // Countdown timer
            let count = 5;
            const countdownEl = document.getElementById('countdown');
            const timer = setInterval(() => {
              count--;
              if (countdownEl) countdownEl.textContent = count;
              if (count <= 0) {
                clearInterval(timer);
                window.close();
              }
            }, 1000);
            
            // Allow manual close by clicking anywhere
            document.addEventListener('click', () => {
              clearInterval(timer);
              window.close();
            });
            
            // Keyboard shortcut to close
            document.addEventListener('keydown', (e) => {
              if (e.key === 'Escape' || e.key === 'Enter') {
                clearInterval(timer);
                window.close();
              }
            });
          </script>
        </body>
      </html>
    `

    return new NextResponse(successHtml, {
      headers: { 
        'Content-Type': 'text/html',
        'X-Frame-Options': 'SAMEORIGIN',
        'X-Content-Type-Options': 'nosniff'
      },
    })

  } catch (error) {
    console.error('Discord OAuth error:', error)
    
    const safeErrorMessage = escapeHtml(error.message)
    
    const errorHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Discord Connection Failed</title>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              padding: 20px; 
              text-align: center; 
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              min-height: 100vh;
              margin: 0;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .container {
              max-width: 600px;
              margin: 0 auto;
              background: white;
              padding: 30px;
              border-radius: 16px;
              box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            }
            .error { 
              color: #dc3545; 
              margin: 20px 0; 
              font-size: 18px;
              font-weight: bold;
            }
            .error-details {
              background: #f8d7da;
              border: 1px solid #f5c6cb;
              padding: 15px;
              border-radius: 8px;
              margin: 20px 0;
              text-align: left;
              font-family: 'SF Mono', Monaco, monospace;
              font-size: 14px;
              word-break: break-word;
            }
            .icon { font-size: 48px; margin-bottom: 20px; }
            .retry-btn {
              background: #007bff;
              color: white;
              padding: 12px 24px;
              border: none;
              border-radius: 8px;
              cursor: pointer;
              font-size: 16px;
              margin: 20px;
              transition: all 0.2s ease;
            }
            .retry-btn:hover {
              background: #0056b3;
              transform: translateY(-1px);
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="icon">🔴</div>
            <h1>Discord Connection Failed</h1>
            <div class="error">An error occurred during authentication</div>
            <div class="error-details">${safeErrorMessage}</div>
            <p>Please try again or contact support if the problem persists.</p>
            <button onclick="window.close()" class="retry-btn">
              Close Window
            </button>
          </div>
          <script>
            console.error('Discord auth error:', '${safeErrorMessage}');
            
            if (window.opener) {
              window.opener.postMessage({ 
                type: 'DISCORD_AUTH_ERROR', 
                error: '${safeErrorMessage}',
                guidance: 'Please check your configuration and try again'
              }, '*');
            }
            
            setTimeout(() => window.close(), 15000);
          </script>
        </body>
      </html>
    `

    return new NextResponse(errorHtml, {
      headers: { 
        'Content-Type': 'text/html',
        'X-Frame-Options': 'SAMEORIGIN',
        'X-Content-Type-Options': 'nosniff'
      },
    })
  }
}
