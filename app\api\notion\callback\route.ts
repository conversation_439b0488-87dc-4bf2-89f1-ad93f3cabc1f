import { NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/client';
import { isSupabaseEnabled } from '@/lib/supabase/config';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    console.log('Callback received:', { code: !!code, state, error });

    if (error || !code || !state) {
      return new Response(`
        <script>
          window.opener?.postMessage({ 
            type: 'NOTION_AUTH_ERROR', 
            error: 'Invalid parameters: ${error || 'No code/state'}' 
          }, '*');
          window.close();
        </script>
      `, {
        headers: { 'Content-Type': 'text/html' },
      });
    }

    if (!isSupabaseEnabled) {
      return new Response(`
        <script>
          window.opener?.postMessage({ 
            type: 'NOTION_AUTH_ERROR', 
            error: 'Database not configured' 
          }, '*');
          window.close();
        </script>
      `, {
        headers: { 'Content-Type': 'text/html' },
      });
    }

    const supabase = createClient();
    if (!supabase) {
      return new Response(`
        <script>
          window.opener?.postMessage({ 
            type: 'NOTION_AUTH_ERROR', 
            error: 'Database client not available' 
          }, '*');
          window.close();
        </script>
      `, {
        headers: { 'Content-Type': 'text/html' },
      });
    }

    const clientId = process.env.NOTION_CLIENT_ID;
    const clientSecret = process.env.NOTION_CLIENT_SECRET;

    if (!clientId || !clientSecret) {
      throw new Error('Missing Notion credentials');
    }

    // Exchange code for access token
    const tokenResponse = await fetch('https://api.notion.com/v1/oauth/token', {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        grant_type: 'authorization_code',
        code,
      }),
    });

    console.log('Token response status:', tokenResponse.status);

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('Token exchange failed:', errorText);
      throw new Error(`Failed to exchange code for token: ${errorText}`);
    }

    const tokenData = await tokenResponse.json();
    console.log('Token data received:', Object.keys(tokenData));

    // 🔥 FIXED: Handle duplicate key with proper UPSERT
    const { data, error: dbError } = await supabase
      .from('notion_auth')
      .upsert({
        user_id: state,
        access_token: tokenData.access_token,
        workspace_name: tokenData.workspace_name || 'Personal Workspace',
        workspace_id: tokenData.workspace_id,
        bot_id: tokenData.bot_id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'user_id',  // 🔥 Key fix: specify conflict resolution
        ignoreDuplicates: false // Update existing record instead of ignoring
      })
      .select();

    if (dbError) {
      console.error('Database error:', dbError);
      throw new Error(`Failed to save authentication data: ${dbError.message}`);
    }

    console.log('Database update successful:', data);

    return new Response(`
      <script>
        window.opener?.postMessage({ type: 'NOTION_AUTH_SUCCESS' }, '*');
        window.close();
      </script>
    `, {
      headers: { 'Content-Type': 'text/html' },
    });

  } catch (error) {
    console.error('Notion callback error:', error);
    return new Response(`
      <script>
        window.opener?.postMessage({ 
          type: 'NOTION_AUTH_ERROR', 
          error: '${error.message}' 
        }, '*');
        window.close();
      </script>
    `, {
      headers: { 'Content-Type': 'text/html' },
    });
  }
}
