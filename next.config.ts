import type { NextConfig } from "next";

const withBundleAnalyzer = require("@next/bundle-analyzer")({
  enabled: process.env.ANALYZE === "true",
});

const nextConfig: NextConfig = withBundleAnalyzer({
  reactStrictMode: true,
  typescript: {
    ignoreBuildErrors: true,
  },
  output: "standalone",
  experimental: {
    optimizePackageImports: ["@phosphor-icons/react"],
    // disable LightningCSS to avoid missing binary
    optimizeCss: false,
  },
  serverExternalPackages: ["shiki", "vscode-oniguruma"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "*.supabase.co",
        port: "",
        pathname: "/storage/v1/object/public/**",
      },
    ],
  },
  eslint: {
    // @todo: remove before going live
    ignoreDuringBuilds: true,
  },
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "Content-Security-Policy",
            value: `
              default-src 'self';
              connect-src 'self' wss: https://api.openai.com https://api.mistral.ai https://api.supabase.com https://uvqumfsecdnmzueqdocd.supabase.co https://api.github.com https://collector.onedollarstats.com https://nx.human.api.nityasha.com;
              img-src 'self' data: https://*.supabase.co;
              script-src 'self';
              style-src 'self' 'unsafe-inline';
            `.replace(/\s{2,}/g, " "), // remove extra whitespace
          },
        ],
      },
    ];
  },
});

export default nextConfig;
