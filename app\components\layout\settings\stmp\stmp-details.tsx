"use client"

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { toast } from "@/components/ui/toast"
import { createClient } from "@/lib/supabase/client"
import { cn } from "@/lib/utils"
import { EnvelopeIcon, PlusIcon } from "@phosphor-icons/react"
import { Loader2, Trash2 } from "lucide-react"
import { useState, useEffect } from "react"
import { useUser } from "@/lib/user-store/provider"

type SMTPAccount = {
  id: number
  name: string
  host: string
  port: number
  username: string
  password: string
  encryption: 'none' | 'ssl' | 'tls'
  from_email: string
  from_name?: string
  is_active: boolean
  user_id: string
  created_at: string
  updated_at: string
}

type SMTPFormData = Omit<SMTPAccount, 'id' | 'user_id' | 'created_at' | 'updated_at'> & {
  id?: number
}

const SMTP_PRESETS = [
  {
    id: "gmail",
    name: "Gmail",
    host: "smtp.gmail.com",
    port: 587,
    encryption: 'tls' as const,
  },
  {
    id: "outlook",
    name: "Outlook",
    host: "smtp-mail.outlook.com",
    port: 587,
    encryption: 'tls' as const,
  },
  {
    id: "yahoo",
    name: "Yahoo",
    host: "smtp.mail.yahoo.com",
    port: 587,
    encryption: 'tls' as const,
  },
  {
    id: "office365",
    name: "Office 365",
    host: "smtp.office365.com",
    port: 587,
    encryption: 'tls' as const,
  },
  {
    id: "custom",
    name: "Custom SMTP",
    host: "",
    port: 587,
    encryption: 'tls' as const,
  },
]

export function SMTPSection() {
  const [smtpAccounts, setSmtpAccounts] = useState<SMTPAccount[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [selectedAccount, setSelectedAccount] = useState<number | "new" | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [accountToDelete, setAccountToDelete] = useState<number | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [supabaseError, setSupabaseError] = useState(false)
  
  const [formData, setFormData] = useState<SMTPFormData>({
    name: "",
    host: "",
    port: 587,
    username: "",
    password: "",
    encryption: 'tls',
    from_email: "",
    from_name: "",
    is_active: true,
  })

  // Get current user - FIXED: Added user context
  const { user } = useUser()
  const supabase = createClient()

  // Fetch SMTP accounts from Supabase - FIXED: Added user filtering
  const fetchAccounts = async () => {
    if (!supabase || !user?.id) {
      setSupabaseError(true)
      setLoading(false)
      return
    }

    setLoading(true)
    try {
      const { data, error } = await supabase
        .from("smtp_accounts")
        .select("*")
        .eq("user_id", user.id) // FIXED: Added user_id filter
        .order("created_at", { ascending: false })

      if (error) throw error
      setSmtpAccounts(data || [])
    } catch (error) {
      console.error("Error fetching accounts:", error)
      toast({
        title: "Error",
        description: "Failed to fetch SMTP accounts",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (user?.id) { // FIXED: Only fetch when user is available
      fetchAccounts()
    }
  }, [user?.id])

  const resetForm = () => {
    setFormData({
      name: "",
      host: "",
      port: 587,
      username: "",
      password: "",
      encryption: 'tls',
      from_email: "",
      from_name: "",
      is_active: true,
    })
    setIsEditing(false)
    setSelectedAccount(null)
  }

  const loadAccountForEditing = (account: SMTPAccount) => {
    setFormData({
      id: account.id,
      name: account.name,
      host: account.host,
      port: account.port,
      username: account.username,
      password: "••••••••", // Don't load the actual password
      encryption: account.encryption,
      from_email: account.from_email,
      from_name: account.from_name || "",
      is_active: account.is_active,
    })
    setIsEditing(true)
    setSelectedAccount(account.id)
  }

  const applyPreset = (presetId: string) => {
    const preset = SMTP_PRESETS.find(p => p.id === presetId)
    if (preset && presetId !== "custom") {
      setFormData(prev => ({
        ...prev,
        host: preset.host,
        port: preset.port,
        encryption: preset.encryption,
        name: prev.name || preset.name, // Set name if empty
      }))
    } else if (presetId === "custom") {
      setFormData(prev => ({
        ...prev,
        host: "",
        port: 587,
        encryption: 'tls',
      }))
    }
  }

  const handleSave = async () => {
    if (!supabase || !user?.id) { // FIXED: Added user check
      toast({
        title: "Error",
        description: "User not authenticated or Supabase not available",
        variant: "destructive",
      })
      return
    }

    // Validation
    if (!formData.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Account name is required",
        variant: "destructive",
      })
      return
    }

    if (!formData.host.trim()) {
      toast({
        title: "Validation Error", 
        description: "SMTP host is required",
        variant: "destructive",
      })
      return
    }

    if (!formData.username.trim()) {
      toast({
        title: "Validation Error",
        description: "Username is required",
        variant: "destructive",
      })
      return
    }

    if (!formData.from_email.trim()) {
      toast({
        title: "Validation Error",
        description: "From email is required",
        variant: "destructive",
      })
      return
    }

    if (!isEditing && !formData.password.trim()) {
      toast({
        title: "Validation Error",
        description: "Password is required",
        variant: "destructive",
      })
      return
    }

    setSaving(true)
    try {
      if (isEditing && formData.id) {
        // Update existing account
        const updateData: any = {
          name: formData.name.trim(),
          host: formData.host.trim(),
          port: formData.port,
          username: formData.username.trim(),
          encryption: formData.encryption,
          from_email: formData.from_email.trim(),
          from_name: formData.from_name?.trim() || null,
          is_active: formData.is_active,
          updated_at: new Date().toISOString(),
        }

        // Only update password if it's not the masked value
        if (formData.password !== "••••••••" && formData.password.trim()) {
          updateData.password = formData.password
        }

        const { error } = await supabase
          .from("smtp_accounts")
          .update(updateData)
          .eq("id", formData.id)
          .eq("user_id", user.id) // FIXED: Added user_id check

        if (error) throw error

        toast({
          title: "Success",
          description: `${formData.name} has been updated successfully.`,
        })
      } else {
        // Create new account - FIXED: Added user_id
        const { error } = await supabase
          .from("smtp_accounts")
          .insert([
            {
              name: formData.name.trim(),
              host: formData.host.trim(),
              port: formData.port,
              username: formData.username.trim(),
              password: formData.password,
              encryption: formData.encryption,
              from_email: formData.from_email.trim(),
              from_name: formData.from_name?.trim() || null,
              is_active: formData.is_active,
              user_id: user.id, // FIXED: This was missing!
            }
          ])

        if (error) throw error

        toast({
          title: "Success",
          description: `${formData.name} has been created successfully.`,
        })
      }

      await fetchAccounts()
      resetForm()
    } catch (error: any) {
      console.error("Error saving account:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to save SMTP account. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const handleDelete = async (id: number) => {
    if (!supabase || !user?.id) { // FIXED: Added user check
      toast({
        title: "Error",
        description: "User not authenticated or Supabase not available",
        variant: "destructive",
      })
      return
    }

    try {
      const { error } = await supabase
        .from("smtp_accounts")
        .delete()
        .eq("id", id)
        .eq("user_id", user.id) // FIXED: Added user_id check

      if (error) throw error

      toast({
        title: "Success",
        description: "SMTP account deleted successfully.",
      })

      await fetchAccounts()
      resetForm()
      setDeleteDialogOpen(false)
      setAccountToDelete(null)
    } catch (error: any) {
      console.error("Error deleting account:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to delete SMTP account.",
        variant: "destructive",
      })
    }
  }

  const handleDeleteClick = (id: number) => {
    setAccountToDelete(id)
    setDeleteDialogOpen(true)
  }

  const handleConfirmDelete = () => {
    if (accountToDelete) {
      handleDelete(accountToDelete)
    }
  }

  // Show loading until user is available - FIXED: Added user loading check
  if (!user?.id) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Loader2 className="size-6 animate-spin" />
          <p className="text-muted-foreground mt-2">Loading user authentication...</p>
        </div>
      </div>
    )
  }

  // Show error if Supabase is not enabled
  if (supabaseError || !supabase) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-muted-foreground">Supabase is not enabled or configured.</p>
          <p className="text-sm text-muted-foreground">Please check your configuration.</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="size-6 animate-spin" />
        <p className="text-muted-foreground ml-2">Loading SMTP accounts...</p>
      </div>
    )
  }

  return (
    <div>
      <h3 className="relative mb-2 inline-flex text-lg font-medium">
        SMTP Accounts
        <span className="text-muted-foreground absolute top-0 -right-7 text-xs">
          new
        </span>
      </h3>
      <p className="text-muted-foreground text-sm">
        Configure SMTP servers for sending emails through the AI assistant.
      </p>
      <p className="text-muted-foreground text-sm">
        Your credentials are stored securely with encryption.
      </p>

      {/* Existing SMTP Accounts Grid */}
      <div className="mt-4 grid grid-cols-2 gap-3 min-[400px]:grid-cols-3 min-[500px]:grid-cols-4">
        {smtpAccounts.map((account) => (
          <button
            key={account.id}
            type="button"
            onClick={() => loadAccountForEditing(account)}
            className={cn(
              "relative flex aspect-square min-w-28 flex-col items-center justify-center gap-2 rounded-lg border p-4 transition-all hover:shadow-md",
              selectedAccount === account.id
                ? "border-primary ring-primary/30 ring-2 bg-primary/5"
                : "border-border hover:border-primary/50"
            )}
          >
            {account.is_active ? (
              <span className="bg-green-500 absolute top-1 right-1 rounded-sm border-[1px] p-1">
                <EnvelopeIcon className="text-white size-3.5" />
              </span>
            ) : (
              <span className="bg-gray-400 absolute top-1 right-1 rounded-sm border-[1px] p-1">
                <EnvelopeIcon className="text-white size-3.5" />
              </span>
            )}
            <EnvelopeIcon className="size-4" />
            <span className="text-center text-sm font-medium">{account.name}</span>
            <span className="text-center text-xs text-muted-foreground">{account.host}</span>
          </button>
        ))}
        
        {/* Add New Button */}
        <button
          type="button"
          onClick={() => {
            resetForm()
            setSelectedAccount("new")
          }}
          className={cn(
            "flex aspect-square min-w-28 flex-col items-center justify-center gap-2 rounded-lg border p-4 transition-all hover:shadow-md",
            selectedAccount === "new"
              ? "border-primary ring-primary/30 ring-2 bg-primary/5"
              : "border-primary border-dashed hover:border-primary/80"
          )}
        >
          <PlusIcon className="size-4" />
          <span className="text-sm">Add New</span>
        </button>
      </div>

      {/* Show account count */}
      {smtpAccounts.length > 0 && (
        <p className="text-muted-foreground text-xs mt-2">
          {smtpAccounts.length} account{smtpAccounts.length !== 1 ? 's' : ''} configured
          {' • '}
          {smtpAccounts.filter(a => a.is_active).length} active
        </p>
      )}

      {/* Configuration Form */}
      {(selectedAccount === "new" || isEditing) && (
        <div className="mt-6 space-y-4 rounded-lg border p-4 bg-card">
          <div className="flex items-center justify-between">
            <h4 className="text-md font-medium">
              {isEditing ? "Edit SMTP Account" : "New SMTP Account"}
            </h4>
            <Button variant="ghost" size="sm" onClick={resetForm}>
              Cancel
            </Button>
          </div>

          {!isEditing && (
            <div className="space-y-2">
              <Label>Quick Setup</Label>
              <Select onValueChange={applyPreset}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a preset or custom" />
                </SelectTrigger>
                <SelectContent>
                  {SMTP_PRESETS.map((preset) => (
                    <SelectItem key={preset.id} value={preset.id}>
                      {preset.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Select a provider to auto-fill common settings
              </p>
            </div>
          )}

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Account Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Production Email"
                disabled={saving}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="host">SMTP Host *</Label>
              <Input
                id="host"
                value={formData.host}
                onChange={(e) => setFormData(prev => ({ ...prev, host: e.target.value }))}
                placeholder="smtp.example.com"
                disabled={saving}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="port">Port *</Label>
              <Input
                id="port"
                type="number"
                value={formData.port}
                onChange={(e) => setFormData(prev => ({ ...prev, port: parseInt(e.target.value) || 587 }))}
                disabled={saving}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="encryption">Encryption</Label>
              <Select
                value={formData.encryption}
                onValueChange={(value: 'none' | 'ssl' | 'tls') => 
                  setFormData(prev => ({ ...prev, encryption: value }))
                }
                disabled={saving}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="ssl">SSL</SelectItem>
                  <SelectItem value="tls">TLS (Recommended)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="username">Username/Email *</Label>
              <Input
                id="username"
                value={formData.username}
                onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                placeholder="<EMAIL>"
                disabled={saving}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Password *</Label>
              <Input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                placeholder={isEditing ? "Leave blank to keep current" : "Enter password"}
                disabled={saving}
              />
              {isEditing && (
                <p className="text-xs text-muted-foreground">
                  Leave blank to keep existing password
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="from_email">From Email *</Label>
              <Input
                id="from_email"
                type="email"
                value={formData.from_email}
                onChange={(e) => setFormData(prev => ({ ...prev, from_email: e.target.value }))}
                placeholder="<EMAIL>"
                disabled={saving}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="from_name">From Name</Label>
              <Input
                id="from_name"
                value={formData.from_name}
                onChange={(e) => setFormData(prev => ({ ...prev, from_name: e.target.value }))}
                placeholder="Your App Name"
                disabled={saving}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
              disabled={saving}
            />
            <Label htmlFor="is_active">Active</Label>
            <span className="text-xs text-muted-foreground">
              Only active accounts can be used for sending emails
            </span>
          </div>

          <div className="flex justify-between pt-4">
            <div>
              {isEditing && (
                <Button
                  variant="outline"
                  onClick={() => handleDeleteClick(formData.id!)}
                  disabled={saving}
                >
                  <Trash2 className="mr-2 size-4" />
                  Delete
                </Button>
              )}
            </div>
            <Button
              onClick={handleSave}
              disabled={
                saving || 
                !formData.name.trim() || 
                !formData.host.trim() || 
                !formData.username.trim() || 
                (!formData.password.trim() && !isEditing) || 
                !formData.from_email.trim()
              }
            >
              {saving ? (
                <Loader2 className="mr-2 size-4 animate-spin" />
              ) : (
                <>{isEditing ? "Update" : "Save"}</>
              )}
            </Button>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete SMTP Account</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this SMTP account? This action cannot be undone and may affect email functionality in your AI assistant.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmDelete}>
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}