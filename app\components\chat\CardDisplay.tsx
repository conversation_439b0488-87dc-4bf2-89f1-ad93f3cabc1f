// CardDisplay.tsx - Fixed all ESLint and TypeScript errors
import React from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { TrendingUp, TrendingDown, Minus, Star, Check, ArrowRight } from 'lucide-react';

// ✅ Fixed: Better type definitions instead of 'any'
type ButtonVariant = 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';

interface CardAction {
  label: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  href?: string;
  onClick?: string;
}

interface CardStats {
  value: string;
  label: string;
  change?: string;
  trend?: 'up' | 'down' | 'neutral';
}

interface CardPrice {
  amount: string;
  currency?: string;
  period?: string;
  features?: string[];
}

// ✅ Fixed: Specific metadata type instead of Record<string, any>
interface CardMetadata {
  rating?: number;
  [key: string]: unknown;
}

interface CardData {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  image?: string;
  avatar?: string;
  badge?: string;
  stats?: CardStats;
  price?: CardPrice;
  actions?: CardAction[];
  metadata?: CardMetadata;
  cardType: 'profile' | 'stats' | 'info' | 'feature' | 'testimonial' | 'pricing' | 'notification';
}

interface CardDisplayConfig {
  cardType: string;
  layout: 'grid' | 'list' | 'carousel';
  columns: number;
  showBorder: boolean;
  showShadow: boolean;
}

interface CardDisplayProps {
  cards: CardData[];
  config: CardDisplayConfig;
}

const ProfileCard = ({ card, config }: { card: CardData; config: CardDisplayConfig }) => (
  <Card className={cn("h-full", !config.showBorder && "border-0", config.showShadow && "shadow-md")}>
    <CardHeader className="text-center">
      {card.avatar && (
        <Avatar className="w-16 h-16 mx-auto mb-4">
          <AvatarImage src={card.avatar} alt={card.title} />
          <AvatarFallback>{card.title.slice(0, 2).toUpperCase()}</AvatarFallback>
        </Avatar>
      )}
      <CardTitle className="text-lg">{card.title}</CardTitle>
      {card.subtitle && <CardDescription>{card.subtitle}</CardDescription>}
      {card.badge && <Badge variant="secondary" className="mt-2">{card.badge}</Badge>}
    </CardHeader>
    {card.description && (
      <CardContent>
        <p className="text-sm text-muted-foreground text-center">{card.description}</p>
      </CardContent>
    )}
    {card.actions && card.actions.length > 0 && (
      <CardFooter className="justify-center gap-2">
        {card.actions.map((action, idx) => (
          <Button
            key={idx}
            variant={action.variant === 'primary' ? 'default' : action.variant as ButtonVariant}
            size="sm"
            onClick={() => action.href && window.open(action.href, '_blank')}
          >
            {action.label}
          </Button>
        ))}
      </CardFooter>
    )}
  </Card>
);

const StatsCard = ({ card, config }: { card: CardData; config: CardDisplayConfig }) => (
  <Card className={cn("h-full", !config.showBorder && "border-0", config.showShadow && "shadow-md")}>
    <CardHeader className="pb-3">
      <div className="flex items-center justify-between">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {card.stats?.label}
        </CardTitle>
        {card.stats?.trend && (
          <div className={cn("flex items-center text-xs", 
            card.stats.trend === 'up' ? 'text-green-600' :
            card.stats.trend === 'down' ? 'text-red-600' : 'text-muted-foreground'
          )}>
            {card.stats.trend === 'up' && <TrendingUp className="w-3 h-3 mr-1" />}
            {card.stats.trend === 'down' && <TrendingDown className="w-3 h-3 mr-1" />}
            {card.stats.trend === 'neutral' && <Minus className="w-3 h-3 mr-1" />}
            {card.stats.change}
          </div>
        )}
      </div>
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{card.stats?.value}</div>
      {card.description && (
        <p className="text-xs text-muted-foreground mt-2">{card.description}</p>
      )}
    </CardContent>
  </Card>
);

const PricingCard = ({ card, config }: { card: CardData; config: CardDisplayConfig }) => (
  <Card className={cn(
    "h-full", 
    !config.showBorder && "border-0", 
    config.showShadow && "shadow-md",
    card.badge && "ring-2 ring-primary"
  )}>
    <CardHeader className="text-center">
      {card.badge && (
        <Badge variant="default" className="mb-4 mx-auto">{card.badge}</Badge>
      )}
      <CardTitle className="text-xl">{card.title}</CardTitle>
      {card.subtitle && <CardDescription>{card.subtitle}</CardDescription>}
      <div className="mt-4">
        <span className="text-3xl font-bold">
          {card.price?.currency}{card.price?.amount}
        </span>
        {card.price?.period && (
          <span className="text-muted-foreground">{card.price.period}</span>
        )}
      </div>
    </CardHeader>
    <CardContent>
      {card.price?.features && (
        <ul className="space-y-2">
          {card.price.features.map((feature, idx) => (
            <li key={idx} className="flex items-center text-sm">
              <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
              {feature}
            </li>
          ))}
        </ul>
      )}
    </CardContent>
    {card.actions && card.actions.length > 0 && (
      <CardFooter>
        {card.actions.map((action, idx) => (
          <Button
            key={idx}
            variant={action.variant === 'primary' ? 'default' : action.variant as ButtonVariant}
            className="w-full"
            onClick={() => action.href && window.open(action.href, '_blank')}
          >
            {action.label}
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        ))}
      </CardFooter>
    )}
  </Card>
);

const FeatureCard = ({ card, config }: { card: CardData; config: CardDisplayConfig }) => (
  <Card className={cn("h-full", !config.showBorder && "border-0", config.showShadow && "shadow-md")}>
    {/* ✅ Fixed: Using Next.js Image component instead of img */}
    {card.image && (
      <div className="aspect-video w-full overflow-hidden rounded-t-lg relative">
        <Image
          src={card.image}
          alt={card.title}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </div>
    )}
    <CardHeader>
      <div className="flex items-start justify-between">
        <div>
          <CardTitle className="text-lg">{card.title}</CardTitle>
          {card.subtitle && <CardDescription>{card.subtitle}</CardDescription>}
        </div>
        {card.badge && <Badge variant="outline">{card.badge}</Badge>}
      </div>
    </CardHeader>
    {card.description && (
      <CardContent>
        <p className="text-sm text-muted-foreground">{card.description}</p>
      </CardContent>
    )}
    {card.actions && card.actions.length > 0 && (
      <CardFooter className="gap-2">
        {card.actions.map((action, idx) => (
          <Button
            key={idx}
            variant={action.variant === 'primary' ? 'default' : action.variant as ButtonVariant}
            onClick={() => action.href && window.open(action.href, '_blank')}
          >
            {action.label}
          </Button>
        ))}
      </CardFooter>
    )}
  </Card>
);

const TestimonialCard = ({ card, config }: { card: CardData; config: CardDisplayConfig }) => (
  <Card className={cn("h-full", !config.showBorder && "border-0", config.showShadow && "shadow-md")}>
    <CardHeader>
      <div className="flex items-center gap-3">
        {card.avatar && (
          <Avatar>
            <AvatarImage src={card.avatar} alt={card.title} />
            <AvatarFallback>{card.title.slice(0, 2).toUpperCase()}</AvatarFallback>
          </Avatar>
        )}
        <div>
          <CardTitle className="text-base">{card.title}</CardTitle>
          {card.subtitle && <CardDescription className="text-sm">{card.subtitle}</CardDescription>}
        </div>
        {/* ✅ Fixed: Optional chaining for card.metadata */}
        {card.metadata?.rating && (
          <div className="flex items-center ml-auto">
            {Array.from({ length: 5 }).map((_, i) => (
              <Star
                key={i}
                className={cn("w-4 h-4", 
                  i < (card.metadata?.rating ?? 0) ? "text-yellow-400 fill-current" : "text-gray-300"
                )}
              />
            ))}
          </div>
        )}
      </div>
    </CardHeader>
    {card.description && (
      <CardContent>
        {/* ✅ Fixed: Escaped quotes using HTML entities */}
        <p className="text-sm italic">&ldquo;{card.description}&rdquo;</p>
      </CardContent>
    )}
  </Card>
);

// ✅ Fixed: Now passing config to all card components
export const CardDisplay: React.FC<CardDisplayProps> = ({ cards, config }) => {
  const renderCard = (card: CardData) => {
    switch (card.cardType) {
      case 'profile':
        return <ProfileCard key={card.id} card={card} config={config} />;
      case 'stats':
        return <StatsCard key={card.id} card={card} config={config} />;
      case 'pricing':
        return <PricingCard key={card.id} card={card} config={config} />;
      case 'feature':
        return <FeatureCard key={card.id} card={card} config={config} />;
      case 'testimonial':
        return <TestimonialCard key={card.id} card={card} config={config} />;
      default:
        return <FeatureCard key={card.id} card={card} config={config} />;
    }
  };

  // ✅ Improved: Better responsive grid classes
  const getGridClasses = () => {
    const cols = Math.min(Math.max(config.columns, 1), 6);
    switch (cols) {
      case 1:
        return "grid-cols-1";
      case 2:
        return "grid-cols-1 md:grid-cols-2";
      case 3:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
      case 4:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4";
      case 5:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5";
      case 6:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6";
      default:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
    }
  };

  const containerClass = cn(
    config.layout === 'grid' && `grid gap-4 ${getGridClasses()}`,
    config.layout === 'list' && "flex flex-col gap-4",
    config.layout === 'carousel' && "flex gap-4 overflow-x-auto pb-4"
  );

  return (
    <div className={containerClass}>
      {cards.map(renderCard)}
    </div>
  );
};
