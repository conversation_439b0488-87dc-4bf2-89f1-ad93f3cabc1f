import type { Message as MessageAISDK } from "@ai-sdk/react";

export function getSources(parts: MessageAISDK["parts"]) {
  const sources = parts
    ?.filter((part) => part.type === "source" || part.type === "tool-invocation")
    .map((part) => {
      if (part.type === "source") return part.source;

      if (part.type === "tool-invocation" && part.toolInvocation.state === "result") {
        const result = part.toolInvocation.result;

        if (part.toolInvocation.toolName === "search" && Array.isArray(result)) {
          return result
            .map((item) => item.link || item.url)
            .filter((url) => typeof url === "string" && url !== "")
            .map((url) => ({ url }));
        }

        if (part.toolInvocation.toolName === "deep_research" && result && typeof result === "object") {
          const deepResults = [];

          if (Array.isArray(result.details)) {
            for (const detail of result.details) {
              if (Array.isArray(detail.search))
                detail.search.forEach((s) => s.link && deepResults.push({ url: s.link }));

              if (Array.isArray(detail.crawl))
                detail.crawl.forEach((c) => c.url && deepResults.push({ url: c.url }));
            }
          }

          // remove duplicates
          return Array.from(new Map(deepResults.map(d => [d.url, d])).values());
        }

        return Array.isArray(result) ? result.flat() : result;
      }

      return null;
    })
    .filter(Boolean)
    .flat();

  return sources?.filter((source) => source && typeof source === "object" && source.url && source.url !== "") || [];
}
